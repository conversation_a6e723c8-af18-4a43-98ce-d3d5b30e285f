# Exemplos de Implementação - Código CSS/React

## 🎯 ScoutBet Classic - Melhorias

### 1. Header Promocional
```tsx
// components/ClassicPromoHeader.tsx
export const ClassicPromoHeader = () => {
  return (
    <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-2 px-4">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <span className="flex items-center gap-2">
          <span className="bg-yellow-400 text-blue-900 px-2 py-1 rounded text-sm font-bold">
            NOVO
          </span>
          <span>Bônus de 100% até R$500 no primeiro depósito!</span>
        </span>
        <button className="bg-yellow-400 text-blue-900 px-4 py-1 rounded hover:bg-yellow-300 transition-colors">
          Aproveitar
        </button>
      </div>
    </div>
  );
};
```

### 2. Tabela de Jogos Aprimorada
```css
/* Classic enhanced table styles */
.classic-game-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.classic-game-table tbody tr {
  transition: all 0.2s ease;
}

.classic-game-table tbody tr:hover {
  background-color: #f0f7ff;
  transform: translateX(4px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.classic-game-table tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

.odds-button {
  background: white;
  border: 2px solid #e5e7eb;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.odds-button:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.odds-button.selected {
  background: #10b981;
  color: white;
  border-color: #10b981;
}
```

## 🌺 ScoutBet Coral - Melhorias

### 1. Hero Section com Gradiente
```tsx
// components/CoralHeroSection.tsx
export const CoralHeroSection = () => {
  return (
    <section className="relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-coral-500 via-orange-400 to-yellow-400 opacity-90" />
      <div className="absolute inset-0 bg-[url('/stadium-bg.jpg')] bg-cover bg-center mix-blend-overlay" />
      
      <div className="relative z-10 px-6 py-16 text-white">
        <h1 className="text-5xl font-bold mb-4 drop-shadow-lg">
          Odds Turbinadas no Brasileirão! ⚡
        </h1>
        <p className="text-xl mb-8 drop-shadow">
          Aposte com as melhores cotações do mercado
        </p>
        <div className="flex gap-4">
          <button className="bg-white text-coral-600 px-8 py-4 rounded-full font-bold text-lg hover:scale-105 transform transition-all shadow-xl">
            Apostar Agora
          </button>
          <button className="bg-transparent border-2 border-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-coral-600 transition-all">
            Ver Promoções
          </button>
        </div>
      </div>
    </section>
  );
};
```

### 2. Cards com Glass Morphism
```css
/* Coral enhanced card styles */
.coral-game-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(255, 127, 80, 0.15);
  transition: all 0.3s ease;
  overflow: hidden;
}

.coral-game-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 48px rgba(255, 127, 80, 0.25);
}

.coral-live-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #ef4444;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
  100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}
```

## 🌙 ScoutBet Modern - Melhorias

### 1. Neon Glow Effects
```css
/* Modern neon styles */
.modern-neon-button {
  background: #1a1a1a;
  color: #00ff88;
  border: 2px solid #00ff88;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modern-neon-button:hover {
  color: #1a1a1a;
  background: #00ff88;
  box-shadow: 0 0 20px #00ff88, 
              0 0 40px #00ff88, 
              0 0 60px #00ff88;
  transform: translateY(-2px);
}

.modern-live-indicator {
  width: 8px;
  height: 8px;
  background: #ff0040;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
  box-shadow: 0 0 10px #ff0040;
  animation: neon-pulse 1.5s ease-in-out infinite;
}

@keyframes neon-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

### 2. Bottom Sheet Bet Slip
```tsx
// components/ModernBottomSheet.tsx
export const ModernBottomSheet = ({ isOpen, onClose }) => {
  return (
    <motion.div
      initial={{ y: "100%" }}
      animate={{ y: isOpen ? 0 : "100%" }}
      transition={{ type: "spring", damping: 25 }}
      className="fixed bottom-0 left-0 right-0 bg-gray-900 rounded-t-3xl shadow-2xl z-50"
    >
      <div className="w-12 h-1 bg-gray-600 rounded-full mx-auto mt-3 mb-4" />
      
      <div className="px-6 pb-8 max-h-[80vh] overflow-y-auto">
        <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
          <span className="text-3xl mr-2">⚡</span>
          Bet Slip
        </h3>
        
        {/* Bet content */}
        <div className="space-y-4">
          {/* Bet items */}
        </div>
        
        <button className="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white py-4 rounded-xl font-bold text-lg mt-6 hover:shadow-lg hover:shadow-green-500/50 transition-all">
          Confirmar Aposta • R$50.00
        </button>
      </div>
    </motion.div>
  );
};
```

## 🎨 Componentes Compartilhados

### Live Match Ticker
```tsx
// shared/components/LiveMatchTicker.tsx
export const LiveMatchTicker = () => {
  return (
    <div className="bg-black text-white py-2 overflow-hidden">
      <div className="flex animate-scroll">
        <div className="flex gap-8 px-4">
          <span className="flex items-center gap-2">
            <span className="live-dot"></span>
            Flamengo 2-1 Palmeiras • 67'
          </span>
          <span className="flex items-center gap-2">
            <span className="live-dot"></span>
            Santos 0-0 Corinthians • 45+2'
          </span>
          {/* More matches */}
        </div>
      </div>
    </div>
  );
};
```

### Animated Odds Change
```css
@keyframes odds-increase {
  0% { background-color: transparent; }
  50% { background-color: #10b981; }
  100% { background-color: transparent; }
}

@keyframes odds-decrease {
  0% { background-color: transparent; }
  50% { background-color: #ef4444; }
  100% { background-color: transparent; }
}

.odds-up {
  animation: odds-increase 1s ease-out;
}

.odds-down {
  animation: odds-decrease 1s ease-out;
}
```

Estes exemplos podem ser implementados progressivamente para melhorar a experiência visual mantendo a identidade única de cada projeto.