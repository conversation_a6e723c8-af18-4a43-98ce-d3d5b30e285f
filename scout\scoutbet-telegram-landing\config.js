// ========== SCOUT BET CONFIGURATION ==========
// IMPORTANT: Replace with your REAL API keys

const SCOUT_CONFIG = {
    // Football API (RapidAPI)
    FOOTBALL_API: {
        url: 'https://api-football-v1.p.rapidapi.com/v3',
        key: 'YOUR_RAPIDAPI_KEY_HERE', // Get from: https://rapidapi.com/api-sports/api/api-football
        headers: {
            'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY_HERE',
            'X-RapidAPI-Host': 'api-football-v1.p.rapidapi.com'
        }
    },
    
    // The Odds API
    ODDS_API: {
        url: 'https://api.the-odds-api.com/v4',
        key: 'YOUR_ODDS_API_KEY_HERE', // Get from: https://the-odds-api.com/
        sports: ['soccer_epl', 'soccer_spain_la_liga', 'soccer_italy_serie_a', 'soccer_germany_bundesliga'],
        regions: 'uk,us,eu',
        markets: 'h2h,spreads,totals'
    },
    
    // OpenWeatherMap API
    WEATHER_API: {
        url: 'https://api.openweathermap.org/data/2.5',
        key: 'YOUR_WEATHER_API_KEY_HERE', // Get from: https://openweathermap.org/api
        units: 'metric'
    },
    
    // PandaScore API (for eSports)
    PANDA_SCORE: {
        url: 'https://api.pandascore.co',
        key: 'YOUR_PANDA_SCORE_KEY_HERE', // Get from: https://pandascore.co/
        games: ['lol', 'csgo', 'dota2', 'valorant']
    },
    
    // Backend API (your own server)
    BACKEND_API: {
        url: 'https://your-backend-api.com/api', // Replace with your backend URL
        endpoints: {
            login: '/auth/login',
            register: '/auth/register',
            profile: '/user/profile',
            recommendations: '/betting/recommendations',
            superOdds: '/betting/super-odds'
        }
    }
};

// API Rate Limits (requests per minute)
const RATE_LIMITS = {
    FOOTBALL_API: 100, // RapidAPI free tier
    ODDS_API: 500,     // The Odds API free tier
    WEATHER_API: 60,   // OpenWeatherMap free tier
    PANDA_SCORE: 1000  // PandaScore free tier
};

// Supported Leagues and Competitions
const SUPPORTED_LEAGUES = {
    FOOTBALL: {
        39: { name: 'Premier League', country: 'England', priority: 1 },
        140: { name: 'La Liga', country: 'Spain', priority: 1 },
        135: { name: 'Serie A', country: 'Italy', priority: 1 },
        78: { name: 'Bundesliga', country: 'Germany', priority: 1 },
        61: { name: 'Ligue 1', country: 'France', priority: 2 },
        2: { name: 'Champions League', country: 'Europe', priority: 1 },
        3: { name: 'Europa League', country: 'Europe', priority: 2 },
        71: { name: 'Brasileirão', country: 'Brazil', priority: 2 },
        253: { name: 'MLS', country: 'USA', priority: 3 }
    },
    ESPORTS: {
        LOL: ['lec', 'lcs', 'lck', 'lpl', 'worlds'],
        CSGO: ['blast-premier', 'esl-pro-league', 'iem'],
        DOTA2: ['dpc', 'the-international'],
        VALORANT: ['vct-international', 'vct-challengers']
    }
};

// Betting Profile Configurations
const PROFILE_CONFIGS = {
    CONSERVATIVE_LOW: {
        maxStake: 5,        // Max 5% of bankroll
        minOdds: 1.2,
        maxOdds: 1.8,
        minConfidence: 90,
        maxBetsPerDay: 3,
        riskTolerance: 'very_low'
    },
    CONSERVATIVE_MEDIUM: {
        maxStake: 8,
        minOdds: 1.8,
        maxOdds: 3.0,
        minConfidence: 75,
        maxBetsPerDay: 5,
        riskTolerance: 'low'
    },
    MEDIUM_MEDIUM: {
        maxStake: 10,
        minOdds: 2.5,
        maxOdds: 5.5,
        minConfidence: 65,
        maxBetsPerDay: 8,
        riskTolerance: 'medium'
    },
    AGGRESSIVE_MEDIUM: {
        maxStake: 15,
        minOdds: 4.0,
        maxOdds: 15.0,
        minConfidence: 55,
        maxBetsPerDay: 10,
        riskTolerance: 'high'
    },
    EXTREME_HIGH: {
        maxStake: 20,
        minOdds: 10.0,
        maxOdds: 50.0,
        minConfidence: 45,
        maxBetsPerDay: 12,
        riskTolerance: 'very_high'
    },
    SUPER_ODDS: {
        maxStake: 2,        // Lower stake for extreme risk
        minOdds: 50.0,
        maxOdds: 200.0,
        minConfidence: 35,
        maxBetsPerDay: 3,   // Limited super odds per day
        riskTolerance: 'extreme',
        multipleType: 'intelligent_correlation'
    }
};

// Analysis Factors and Weights
const ANALYSIS_FACTORS = {
    // Primary factors (higher weight)
    FORM: {
        weight: 0.25,
        description: 'Recent team performance (last 5 games)',
        dataSource: 'FOOTBALL_API'
    },
    HEAD_TO_HEAD: {
        weight: 0.15,
        description: 'Historical matchup results',
        dataSource: 'FOOTBALL_API'
    },
    HOME_ADVANTAGE: {
        weight: 0.15,
        description: 'Home field advantage statistics',
        dataSource: 'FOOTBALL_API'
    },
    INJURIES: {
        weight: 0.15,
        description: 'Key player injuries and suspensions',
        dataSource: 'FOOTBALL_API'
    },
    
    // Secondary factors (medium weight)
    TACTICAL: {
        weight: 0.10,
        description: 'Playing style compatibility',
        dataSource: 'FOOTBALL_API'
    },
    MOTIVATION: {
        weight: 0.10,
        description: 'Match importance and context',
        dataSource: 'ANALYSIS'
    },
    
    // Tertiary factors (lower weight)
    WEATHER: {
        weight: 0.05,
        description: 'Weather conditions impact',
        dataSource: 'WEATHER_API'
    },
    FATIGUE: {
        weight: 0.05,
        description: 'Rest days and fixture congestion',
        dataSource: 'FOOTBALL_API'
    },
    PSYCHOLOGICAL: {
        weight: 0.05,
        description: 'Mental factors and pressure',
        dataSource: 'ANALYSIS'
    }
};

// Market Analysis Configuration
const MARKET_CONFIG = {
    // Minimum number of bookmakers for reliable odds
    MIN_BOOKMAKERS: 3,
    
    // Odds movement thresholds
    SIGNIFICANT_MOVEMENT: 0.1, // 10% change
    MAJOR_MOVEMENT: 0.2,       // 20% change
    
    // Value betting thresholds
    MIN_VALUE: 0.05,           // 5% minimum expected value
    GOOD_VALUE: 0.10,          // 10% good expected value
    EXCELLENT_VALUE: 0.20,     // 20% excellent expected value
    
    // Market efficiency indicators
    MAX_MARGIN: 0.08,          // 8% maximum bookmaker margin
    EFFICIENT_VARIANCE: 0.02,  // 2% maximum odds variance
    
    // Correlation analysis
    NEGATIVE_CORRELATION_THRESHOLD: -0.3,
    POSITIVE_CORRELATION_THRESHOLD: 0.3,
    MAX_CORRELATION_MATCHES: 5
};

// Error Handling and Fallbacks
const ERROR_CONFIG = {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,         // 1 second
    TIMEOUT: 10000,            // 10 seconds
    
    FALLBACK_ODDS: {
        home: 2.0,
        draw: 3.0,
        away: 2.0
    },
    
    FALLBACK_CONFIDENCE: 50,
    
    ERROR_MESSAGES: {
        API_LIMIT: 'API rate limit exceeded. Please try again later.',
        NO_DATA: 'No data available for this match.',
        NETWORK_ERROR: 'Network error. Please check your connection.',
        INVALID_RESPONSE: 'Invalid response from API.',
        AUTHENTICATION: 'Authentication failed. Please check your API keys.'
    }
};

// Development vs Production Settings
const ENVIRONMENT = {
    isDevelopment: window.location.hostname === 'localhost',
    
    // Development settings
    DEV: {
        enableLogging: true,
        mockDataFallback: false, // NEVER use mock data
        debugMode: true,
        showDetailedErrors: true
    },
    
    // Production settings
    PROD: {
        enableLogging: false,
        mockDataFallback: false, // NEVER use mock data
        debugMode: false,
        showDetailedErrors: false
    }
};

// Utility function to get current environment config
function getEnvironmentConfig() {
    return ENVIRONMENT.isDevelopment ? ENVIRONMENT.DEV : ENVIRONMENT.PROD;
}

// Utility function to log messages (only in development)
function debugLog(message, data = null) {
    const config = getEnvironmentConfig();
    if (config.enableLogging) {
        console.log(`[Scout Bet Debug] ${message}`, data);
    }
}

// Utility function to handle errors
function handleError(error, context = '') {
    const config = getEnvironmentConfig();
    
    if (config.showDetailedErrors) {
        console.error(`[Scout Bet Error] ${context}:`, error);
    }
    
    // Log error to analytics service (in production)
    if (!ENVIRONMENT.isDevelopment) {
        // Send error to your analytics service
        // analytics.track('error', { error: error.message, context });
    }
    
    return ERROR_CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
}

// Export configuration for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SCOUT_CONFIG,
        RATE_LIMITS,
        SUPPORTED_LEAGUES,
        PROFILE_CONFIGS,
        ANALYSIS_FACTORS,
        MARKET_CONFIG,
        ERROR_CONFIG,
        ENVIRONMENT,
        getEnvironmentConfig,
        debugLog,
        handleError
    };
}
