-- ========== SCOUT BET - SQL SIMPLES QUE FUNCIONA ==========
-- Copie e cole este SQL no Supabase SQL Editor

-- 1. <PERSON>riar tabela de usuários
CREATE TABLE IF NOT EXISTS public.users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR NOT NULL UNIQUE,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. <PERSON><PERSON>r tabel<PERSON> de perfis
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    profile_type VARCHAR NOT NULL DEFAULT 'CONSERVATIVE_LOW',
    bankroll DECIMAL DEFAULT 1000.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. <PERSON><PERSON>r tabela de apostas
CREATE TABLE IF NOT EXISTS public.bets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    match_info JSONB NOT NULL,
    prediction JSONB NOT NULL,
    stake DECIMAL NOT NULL,
    odds DECIMAL NOT NULL,
    status VARCHAR DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Criar tabela de estatísticas
CREATE TABLE IF NOT EXISTS public.user_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    total_bets INTEGER DEFAULT 0,
    won_bets INTEGER DEFAULT 0,
    total_staked DECIMAL DEFAULT 0,
    total_won DECIMAL DEFAULT 0,
    roi DECIMAL DEFAULT 0,
    win_rate DECIMAL DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Criar tabela de configurações do sistema
CREATE TABLE IF NOT EXISTS public.system_config (
    key VARCHAR PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Inserir configurações básicas
INSERT INTO public.system_config (key, value, description) VALUES
('betting_profiles', '{
    "CONSERVATIVE_LOW": {"name": "🛡️ CONSERVADOR", "min_confidence": 90, "max_stake": 5},
    "CONSERVATIVE_MEDIUM": {"name": "⚖️ EQUILIBRADO", "min_confidence": 75, "max_stake": 8},
    "MEDIUM_MEDIUM": {"name": "📊 BALANCEADO", "min_confidence": 65, "max_stake": 10},
    "AGGRESSIVE_MEDIUM": {"name": "🔥 AGRESSIVO", "min_confidence": 55, "max_stake": 15},
    "EXTREME_HIGH": {"name": "⚡ EXTREMO", "min_confidence": 45, "max_stake": 20},
    "SUPER_ODDS": {"name": "💎 SUPER ODDS", "min_confidence": 35, "max_stake": 2}
}', 'Perfis de apostas'),

('analysis_weights', '{
    "form": 0.25,
    "h2h": 0.15,
    "home_advantage": 0.15,
    "injuries": 0.15,
    "tactical": 0.10,
    "motivation": 0.10,
    "weather": 0.05,
    "fatigue": 0.05,
    "psychological": 0.05
}', 'Pesos de análise'),

('api_limits', '{
    "football_api": 100,
    "odds_api": 500,
    "weather_api": 60,
    "panda_score": 1000
}', 'Limites das APIs')
ON CONFLICT (key) DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = NOW();

-- 7. Habilitar Row Level Security (básico)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_statistics ENABLE ROW LEVEL SECURITY;

-- 8. Criar políticas básicas de segurança
CREATE POLICY "Permitir leitura para todos" ON public.system_config FOR SELECT USING (true);
CREATE POLICY "Permitir tudo para usuários autenticados" ON public.users FOR ALL USING (true);
CREATE POLICY "Permitir tudo para perfis" ON public.user_profiles FOR ALL USING (true);
CREATE POLICY "Permitir tudo para apostas" ON public.bets FOR ALL USING (true);
CREATE POLICY "Permitir tudo para estatísticas" ON public.user_statistics FOR ALL USING (true);

-- 9. Verificar se funcionou
SELECT 'Configuração concluída com sucesso!' as status;
SELECT key, description FROM public.system_config;

-- 10. Mostrar tabelas criadas
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'user_profiles', 'bets', 'user_statistics', 'system_config')
ORDER BY table_name;
