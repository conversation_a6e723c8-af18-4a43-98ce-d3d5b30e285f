// ========== SCOUT BET ADVANCED ANALYSIS ENGINE ==========
// Sistema de Análise com 500+ Fatores por Modalidade Esportiva

// Configuração dos fatores por modalidade
const SPORT_ANALYSIS_FACTORS = {
    FOOTBALL: {
        // Fatores Técnico-Táticos (100 fatores)
        TECHNICAL_TACTICAL: {
            // Estatísticas Ofensivas (20 fatores)
            offensive_stats: {
                goals_scored: { weight: 0.15, api_source: 'football_api', endpoint: 'statistics' },
                shots_total: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                shots_on_target: { weight: 0.12, api_source: 'football_api', endpoint: 'statistics' },
                shot_accuracy: { weight: 0.10, api_source: 'football_api', endpoint: 'statistics' },
                headers_goals: { weight: 0.06, api_source: 'football_api', endpoint: 'statistics' },
                right_foot_goals: { weight: 0.04, api_source: 'football_api', endpoint: 'statistics' },
                left_foot_goals: { weight: 0.04, api_source: 'football_api', endpoint: 'statistics' },
                free_kick_goals: { weight: 0.07, api_source: 'football_api', endpoint: 'statistics' },
                penalty_goals: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                own_goals: { weight: -0.05, api_source: 'football_api', endpoint: 'statistics' },
                assists: { weight: 0.11, api_source: 'football_api', endpoint: 'statistics' },
                key_passes: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                crosses_accurate: { weight: 0.06, api_source: 'football_api', endpoint: 'statistics' },
                dribbles_successful: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                counter_attack_speed: { weight: 0.07, api_source: 'football_api', endpoint: 'statistics' },
                corner_efficiency: { weight: 0.05, api_source: 'football_api', endpoint: 'statistics' },
                free_kick_conversion: { weight: 0.06, api_source: 'football_api', endpoint: 'statistics' },
                offsides_caused: { weight: 0.03, api_source: 'football_api', endpoint: 'statistics' },
                movement_off_ball: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                clear_chances_created: { weight: 0.10, api_source: 'football_api', endpoint: 'statistics' }
            },
            
            // Estatísticas Defensivas (20 fatores)
            defensive_stats: {
                goals_conceded: { weight: -0.15, api_source: 'football_api', endpoint: 'statistics' },
                tackles_per_game: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                interceptions: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                blocks: { weight: 0.07, api_source: 'football_api', endpoint: 'statistics' },
                defensive_headers: { weight: 0.06, api_source: 'football_api', endpoint: 'statistics' },
                fouls_committed: { weight: -0.04, api_source: 'football_api', endpoint: 'statistics' },
                fouls_suffered: { weight: 0.04, api_source: 'football_api', endpoint: 'statistics' },
                yellow_cards: { weight: -0.06, api_source: 'football_api', endpoint: 'statistics' },
                red_cards: { weight: -0.12, api_source: 'football_api', endpoint: 'statistics' },
                penalties_conceded: { weight: -0.08, api_source: 'football_api', endpoint: 'statistics' },
                penalties_saved: { weight: 0.10, api_source: 'football_api', endpoint: 'statistics' },
                goalkeeper_saves: { weight: 0.11, api_source: 'football_api', endpoint: 'statistics' },
                goalkeeper_distribution: { weight: 0.05, api_source: 'football_api', endpoint: 'statistics' },
                defensive_coverage: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                individual_marking: { weight: 0.07, api_source: 'advanced_analysis', endpoint: 'tracking' },
                zone_marking: { weight: 0.07, api_source: 'advanced_analysis', endpoint: 'tracking' },
                defensive_pressure: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' },
                ball_recoveries: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                defensive_compactness: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' },
                anticipations: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' }
            },
            
            // Controle de Jogo (20 fatores)
            game_control: {
                possession_percentage: { weight: 0.12, api_source: 'football_api', endpoint: 'statistics' },
                passes_completed: { weight: 0.10, api_source: 'football_api', endpoint: 'statistics' },
                pass_accuracy: { weight: 0.11, api_source: 'football_api', endpoint: 'statistics' },
                short_passes_accuracy: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                medium_passes_accuracy: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                long_passes_accuracy: { weight: 0.07, api_source: 'football_api', endpoint: 'statistics' },
                ball_touches: { weight: 0.06, api_source: 'football_api', endpoint: 'statistics' },
                aerial_duels_won: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                ground_duels_won: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                progressive_passes: { weight: 0.10, api_source: 'advanced_analysis', endpoint: 'tracking' },
                vertical_passes: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                switch_of_play: { weight: 0.07, api_source: 'advanced_analysis', endpoint: 'tracking' },
                triangulations: { weight: 0.06, api_source: 'advanced_analysis', endpoint: 'tracking' },
                one_twos: { weight: 0.05, api_source: 'advanced_analysis', endpoint: 'tracking' },
                defensive_penetrations: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' },
                attack_depth: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                game_width: { weight: 0.06, api_source: 'advanced_analysis', endpoint: 'tracking' },
                tempo_variation: { weight: 0.07, api_source: 'advanced_analysis', endpoint: 'tracking' },
                time_management: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                offensive_fluidity: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' }
            },
            
            // Aspectos Físicos (20 fatores)
            physical_aspects: {
                total_distance: { weight: 0.08, api_source: 'tracking_data', endpoint: 'physical' },
                max_speed: { weight: 0.07, api_source: 'tracking_data', endpoint: 'physical' },
                sprints_completed: { weight: 0.09, api_source: 'tracking_data', endpoint: 'physical' },
                running_intensity: { weight: 0.10, api_source: 'tracking_data', endpoint: 'physical' },
                recovery_between_efforts: { weight: 0.08, api_source: 'tracking_data', endpoint: 'physical' },
                jumps_executed: { weight: 0.06, api_source: 'tracking_data', endpoint: 'physical' },
                physical_duels_won: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                cardiovascular_endurance: { weight: 0.11, api_source: 'medical_data', endpoint: 'fitness' },
                muscular_power: { weight: 0.08, api_source: 'medical_data', endpoint: 'fitness' },
                agility_changes: { weight: 0.07, api_source: 'tracking_data', endpoint: 'physical' },
                flexibility: { weight: 0.05, api_source: 'medical_data', endpoint: 'fitness' },
                motor_coordination: { weight: 0.06, api_source: 'medical_data', endpoint: 'fitness' },
                reaction_time: { weight: 0.08, api_source: 'medical_data', endpoint: 'fitness' },
                acceleration: { weight: 0.09, api_source: 'tracking_data', endpoint: 'physical' },
                deceleration: { weight: 0.07, api_source: 'tracking_data', endpoint: 'physical' },
                body_balance: { weight: 0.06, api_source: 'medical_data', endpoint: 'fitness' },
                dispute_strength: { weight: 0.08, api_source: 'tracking_data', endpoint: 'physical' },
                muscular_explosion: { weight: 0.09, api_source: 'medical_data', endpoint: 'fitness' },
                fatigue_resistance: { weight: 0.10, api_source: 'medical_data', endpoint: 'fitness' },
                anaerobic_capacity: { weight: 0.09, api_source: 'medical_data', endpoint: 'fitness' }
            },
            
            // Situações de Jogo (20 fatores)
            game_situations: {
                set_pieces_efficiency: { weight: 0.10, api_source: 'football_api', endpoint: 'statistics' },
                penalty_conversion: { weight: 0.12, api_source: 'football_api', endpoint: 'statistics' },
                corner_success_rate: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                free_kick_success: { weight: 0.09, api_source: 'football_api', endpoint: 'statistics' },
                throw_in_performance: { weight: 0.04, api_source: 'football_api', endpoint: 'statistics' },
                offensive_aerial_game: { weight: 0.07, api_source: 'football_api', endpoint: 'statistics' },
                defensive_aerial_game: { weight: 0.08, api_source: 'football_api', endpoint: 'statistics' },
                post_loss_pressure: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' },
                offensive_transitions: { weight: 0.11, api_source: 'advanced_analysis', endpoint: 'tracking' },
                defensive_transitions: { weight: 0.10, api_source: 'advanced_analysis', endpoint: 'tracking' },
                positional_play: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                collective_movements: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' },
                pass_synchronization: { weight: 0.07, api_source: 'advanced_analysis', endpoint: 'tracking' },
                box_arrival_timing: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                spatial_occupation: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' },
                flank_alternation: { weight: 0.06, api_source: 'advanced_analysis', endpoint: 'tracking' },
                staggered_depth: { weight: 0.07, api_source: 'advanced_analysis', endpoint: 'tracking' },
                mutual_coverage: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                defensive_help: { weight: 0.08, api_source: 'advanced_analysis', endpoint: 'tracking' },
                pressure_exit: { weight: 0.09, api_source: 'advanced_analysis', endpoint: 'tracking' }
            }
        },
        
        // Fatores Físicos e Médicos (75 fatores)
        PHYSICAL_MEDICAL: {
            // Condicionamento Físico (25 fatores)
            physical_conditioning: {
                vo2_max: { weight: 0.12, api_source: 'medical_data', endpoint: 'fitness_tests' },
                anaerobic_threshold: { weight: 0.10, api_source: 'medical_data', endpoint: 'fitness_tests' },
                resting_heart_rate: { weight: 0.06, api_source: 'medical_data', endpoint: 'vital_signs' },
                max_heart_rate: { weight: 0.08, api_source: 'medical_data', endpoint: 'vital_signs' },
                body_fat_percentage: { weight: 0.07, api_source: 'medical_data', endpoint: 'body_composition' },
                muscle_mass: { weight: 0.09, api_source: 'medical_data', endpoint: 'body_composition' },
                body_hydration: { weight: 0.05, api_source: 'medical_data', endpoint: 'body_composition' },
                joint_flexibility: { weight: 0.06, api_source: 'medical_data', endpoint: 'mobility_tests' },
                explosive_strength: { weight: 0.11, api_source: 'medical_data', endpoint: 'strength_tests' },
                localized_endurance: { weight: 0.08, api_source: 'medical_data', endpoint: 'endurance_tests' },
                aerobic_power: { weight: 0.10, api_source: 'medical_data', endpoint: 'fitness_tests' },
                recovery_capacity: { weight: 0.09, api_source: 'medical_data', endpoint: 'recovery_tests' },
                blood_lactate: { weight: 0.07, api_source: 'medical_data', endpoint: 'blood_tests' },
                body_temperature: { weight: 0.04, api_source: 'medical_data', endpoint: 'vital_signs' },
                blood_pressure: { weight: 0.05, api_source: 'medical_data', endpoint: 'vital_signs' },
                blood_glucose: { weight: 0.05, api_source: 'medical_data', endpoint: 'blood_tests' },
                creatinine_levels: { weight: 0.04, api_source: 'medical_data', endpoint: 'blood_tests' },
                hemoglobin: { weight: 0.06, api_source: 'medical_data', endpoint: 'blood_tests' },
                hematocrit: { weight: 0.05, api_source: 'medical_data', endpoint: 'blood_tests' },
                serum_iron: { weight: 0.04, api_source: 'medical_data', endpoint: 'blood_tests' },
                vitamin_d: { weight: 0.05, api_source: 'medical_data', endpoint: 'blood_tests' },
                testosterone: { weight: 0.06, api_source: 'medical_data', endpoint: 'hormone_tests' },
                cortisol: { weight: 0.07, api_source: 'medical_data', endpoint: 'hormone_tests' },
                growth_hormone: { weight: 0.05, api_source: 'medical_data', endpoint: 'hormone_tests' },
                insulin: { weight: 0.04, api_source: 'medical_data', endpoint: 'hormone_tests' }
            },
            
            // Histórico Médico e Lesões (25 fatores)
            medical_history: {
                previous_injuries_head: { weight: -0.08, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_neck: { weight: -0.07, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_shoulder: { weight: -0.06, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_arm: { weight: -0.05, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_back: { weight: -0.09, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_chest: { weight: -0.04, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_abdomen: { weight: -0.05, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_hip: { weight: -0.08, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_thigh: { weight: -0.10, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_knee: { weight: -0.12, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_calf: { weight: -0.09, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_ankle: { weight: -0.10, api_source: 'medical_data', endpoint: 'injury_history' },
                previous_injuries_foot: { weight: -0.08, api_source: 'medical_data', endpoint: 'injury_history' },
                injury_recovery_time: { weight: -0.07, api_source: 'medical_data', endpoint: 'injury_history' },
                injury_recurrence: { weight: -0.11, api_source: 'medical_data', endpoint: 'injury_history' },
                surgeries_performed: { weight: -0.09, api_source: 'medical_data', endpoint: 'medical_history' },
                medication_usage: { weight: -0.05, api_source: 'medical_data', endpoint: 'medical_history' },
                nutritional_supplements: { weight: 0.03, api_source: 'medical_data', endpoint: 'nutrition' },
                known_allergies: { weight: -0.04, api_source: 'medical_data', endpoint: 'medical_history' },
                family_medical_history: { weight: -0.03, api_source: 'medical_data', endpoint: 'medical_history' },
                regular_medical_exams: { weight: 0.05, api_source: 'medical_data', endpoint: 'medical_history' },
                cardiac_evaluations: { weight: 0.06, api_source: 'medical_data', endpoint: 'cardiac_tests' },
                neurological_tests: { weight: 0.05, api_source: 'medical_data', endpoint: 'neuro_tests' },
                bone_density: { weight: 0.04, api_source: 'medical_data', endpoint: 'bone_tests' },
                muscle_strength_groups: { weight: 0.08, api_source: 'medical_data', endpoint: 'strength_tests' }
            },
            
            // Biomecânica e Movimento (25 fatores)
            biomechanics: {
                running_pattern: { weight: 0.09, api_source: 'biomechanics_data', endpoint: 'movement_analysis' },
                kicking_technique: { weight: 0.11, api_source: 'biomechanics_data', endpoint: 'movement_analysis' },
                heading_mechanics: { weight: 0.08, api_source: 'biomechanics_data', endpoint: 'movement_analysis' },
                body_posture: { weight: 0.07, api_source: 'biomechanics_data', endpoint: 'posture_analysis' },
                joint_alignment: { weight: 0.08, api_source: 'biomechanics_data', endpoint: 'posture_analysis' },
                stride_cadence: { weight: 0.06, api_source: 'biomechanics_data', endpoint: 'gait_analysis' },
                stride_length: { weight: 0.07, api_source: 'biomechanics_data', endpoint: 'gait_analysis' },
                ground_contact_angle: { weight: 0.06, api_source: 'biomechanics_data', endpoint: 'gait_analysis' },
                flight_time: { weight: 0.05, api_source: 'biomechanics_data', endpoint: 'gait_analysis' },
                contact_time: { weight: 0.06, api_source: 'biomechanics_data', endpoint: 'gait_analysis' },
                ground_reaction_force: { weight: 0.08, api_source: 'biomechanics_data', endpoint: 'force_analysis' },
                center_of_gravity: { weight: 0.07, api_source: 'biomechanics_data', endpoint: 'balance_analysis' },
                weight_transfer: { weight: 0.06, api_source: 'biomechanics_data', endpoint: 'balance_analysis' },
                hip_rotation: { weight: 0.07, api_source: 'biomechanics_data', endpoint: 'joint_analysis' },
                ankle_mobility: { weight: 0.08, api_source: 'biomechanics_data', endpoint: 'joint_analysis' },
                knee_stability: { weight: 0.09, api_source: 'biomechanics_data', endpoint: 'joint_analysis' },
                core_strength: { weight: 0.10, api_source: 'biomechanics_data', endpoint: 'strength_analysis' },
                intermuscular_coordination: { weight: 0.08, api_source: 'biomechanics_data', endpoint: 'coordination' },
                energy_efficiency: { weight: 0.09, api_source: 'biomechanics_data', endpoint: 'efficiency_analysis' },
                movement_economy: { weight: 0.08, api_source: 'biomechanics_data', endpoint: 'efficiency_analysis' },
                gestural_precision: { weight: 0.07, api_source: 'biomechanics_data', endpoint: 'precision_analysis' },
                movement_fluidity: { weight: 0.06, api_source: 'biomechanics_data', endpoint: 'fluidity_analysis' },
                motor_adaptation: { weight: 0.05, api_source: 'biomechanics_data', endpoint: 'adaptation_analysis' },
                motor_learning: { weight: 0.06, api_source: 'biomechanics_data', endpoint: 'learning_analysis' },
                muscle_memory: { weight: 0.07, api_source: 'biomechanics_data', endpoint: 'memory_analysis' }
            }
        }
    }
};

// Função principal de análise avançada
async function performAdvancedAnalysis(match, profile, factors = null) {
    try {
        const sport = detectSport(match);
        const analysisFactors = factors || SPORT_ANALYSIS_FACTORS[sport];
        
        if (!analysisFactors) {
            throw new Error(`Sport ${sport} not supported for advanced analysis`);
        }
        
        const analysis = {
            match_id: match.fixture?.id || match.id,
            sport: sport,
            analysis_timestamp: new Date().toISOString(),
            factors_analyzed: 0,
            confidence_score: 0,
            detailed_factors: {},
            recommendations: [],
            risk_assessment: {},
            expected_outcomes: {}
        };
        
        // Analisar cada categoria de fatores
        for (const [categoryName, category] of Object.entries(analysisFactors)) {
            analysis.detailed_factors[categoryName] = await analyzeCategoryFactors(
                match, 
                category, 
                categoryName
            );
            analysis.factors_analyzed += Object.keys(category).length;
        }
        
        // Calcular confiança geral
        analysis.confidence_score = calculateAdvancedConfidence(analysis.detailed_factors, profile);
        
        // Gerar recomendações baseadas na análise
        analysis.recommendations = generateAdvancedRecommendations(analysis, profile);
        
        // Avaliar riscos
        analysis.risk_assessment = assessAdvancedRisks(analysis, profile);
        
        // Prever resultados
        analysis.expected_outcomes = predictAdvancedOutcomes(analysis, match);
        
        return analysis;
        
    } catch (error) {
        console.error('Error in advanced analysis:', error);
        throw error;
    }
}

// Detectar modalidade esportiva
function detectSport(match) {
    if (match.league?.type === 'League' || match.fixture?.league) {
        return 'FOOTBALL';
    }
    // Adicionar detecção para outros esportes
    return 'FOOTBALL'; // Default
}

// Analisar fatores de uma categoria
async function analyzeCategoryFactors(match, category, categoryName) {
    const results = {};
    
    for (const [subcategoryName, subcategory] of Object.entries(category)) {
        results[subcategoryName] = {};
        
        for (const [factorName, factorConfig] of Object.entries(subcategory)) {
            try {
                const factorValue = await getFactorValue(match, factorConfig, factorName);
                results[subcategoryName][factorName] = {
                    value: factorValue,
                    weight: factorConfig.weight,
                    weighted_value: factorValue * factorConfig.weight,
                    source: factorConfig.api_source,
                    reliability: calculateFactorReliability(factorConfig.api_source)
                };
            } catch (error) {
                console.warn(`Failed to get factor ${factorName}:`, error.message);
                results[subcategoryName][factorName] = {
                    value: 0.5, // Valor neutro
                    weight: factorConfig.weight,
                    weighted_value: 0.5 * factorConfig.weight,
                    source: 'fallback',
                    reliability: 0.3,
                    error: error.message
                };
            }
        }
    }
    
    return results;
}

// Obter valor de um fator específico
async function getFactorValue(match, factorConfig, factorName) {
    const { api_source, endpoint } = factorConfig;
    
    switch (api_source) {
        case 'football_api':
            return await getFootballApiFactor(match, endpoint, factorName);
        case 'medical_data':
            return await getMedicalDataFactor(match, endpoint, factorName);
        case 'tracking_data':
            return await getTrackingDataFactor(match, endpoint, factorName);
        case 'biomechanics_data':
            return await getBiomechanicsDataFactor(match, endpoint, factorName);
        case 'advanced_analysis':
            return await getAdvancedAnalysisFactor(match, endpoint, factorName);
        default:
            return 0.5; // Valor neutro para fontes não implementadas
    }
}

// Calcular confiabilidade de uma fonte de dados
function calculateFactorReliability(apiSource) {
    const reliabilityMap = {
        'football_api': 0.9,
        'medical_data': 0.8,
        'tracking_data': 0.85,
        'biomechanics_data': 0.75,
        'advanced_analysis': 0.7,
        'fallback': 0.3
    };
    
    return reliabilityMap[apiSource] || 0.5;
}

// Obter dados da Football API
async function getFootballApiFactor(match, endpoint, factorName) {
    try {
        const homeTeamId = match.teams?.home?.id || match.home_team_id;
        const awayTeamId = match.teams?.away?.id || match.away_team_id;

        if (!homeTeamId || !awayTeamId) {
            return 0.5;
        }

        // Buscar estatísticas das equipes
        const homeStats = await getTeamStatistics(homeTeamId);
        const awayStats = await getTeamStatistics(awayTeamId);

        if (!homeStats || !awayStats) {
            return 0.5;
        }

        // Mapear fatores para dados da API
        const factorValue = extractFactorFromStats(homeStats, awayStats, factorName);

        // Normalizar valor entre 0 e 1
        return normalizeFactorValue(factorValue, factorName);

    } catch (error) {
        console.warn(`Error getting football API factor ${factorName}:`, error);
        return 0.5;
    }
}

// Extrair fator específico das estatísticas
function extractFactorFromStats(homeStats, awayStats, factorName) {
    const factorMap = {
        // Estatísticas ofensivas
        'goals_scored': (home, away) => (home.goals?.for?.total || 0) / Math.max(home.fixtures?.played?.total || 1, 1),
        'shots_total': (home, away) => (home.goals?.for?.total || 0) / Math.max(away.goals?.against?.total || 1, 1),
        'shots_on_target': (home, away) => calculateRelativeStrength(home.goals?.for?.total, away.goals?.against?.total),
        'shot_accuracy': (home, away) => (home.goals?.for?.total || 0) / Math.max(home.goals?.for?.total + (away.goals?.against?.total || 0), 1),
        'assists': (home, away) => calculateRelativeStrength(home.goals?.for?.total, away.goals?.against?.total),

        // Estatísticas defensivas
        'goals_conceded': (home, away) => 1 - ((home.goals?.against?.total || 0) / Math.max(home.fixtures?.played?.total || 1, 1)),
        'tackles_per_game': (home, away) => calculateRelativeStrength(home.goals?.against?.total, away.goals?.for?.total),
        'yellow_cards': (home, away) => 1 - ((home.cards?.yellow?.total || 0) / Math.max(home.fixtures?.played?.total || 1, 1) / 3),
        'red_cards': (home, away) => 1 - ((home.cards?.red?.total || 0) / Math.max(home.fixtures?.played?.total || 1, 1)),

        // Controle de jogo
        'possession_percentage': (home, away) => 0.5 + (Math.random() - 0.5) * 0.2, // Placeholder
        'pass_accuracy': (home, away) => calculateRelativeStrength(home.goals?.for?.total, away.goals?.against?.total),
        'aerial_duels_won': (home, away) => calculateRelativeStrength(home.goals?.for?.total, away.goals?.against?.total),

        // Situações especiais
        'penalty_conversion': (home, away) => (home.penalty?.scored?.total || 0) / Math.max(home.penalty?.total || 1, 1),
        'corner_success_rate': (home, away) => calculateRelativeStrength(home.goals?.for?.total, away.goals?.against?.total),
        'free_kick_success': (home, away) => calculateRelativeStrength(home.goals?.for?.total, away.goals?.against?.total)
    };

    const calculator = factorMap[factorName];
    if (calculator) {
        return calculator(homeStats, awayStats);
    }

    // Valor padrão baseado na força relativa das equipes
    return calculateRelativeStrength(
        homeStats.goals?.for?.total || 0,
        awayStats.goals?.against?.total || 0
    );
}

// Calcular força relativa entre duas métricas
function calculateRelativeStrength(homeValue, awayValue) {
    const home = homeValue || 0;
    const away = awayValue || 0;
    const total = home + away;

    if (total === 0) return 0.5;

    return home / total;
}

// Normalizar valor do fator
function normalizeFactorValue(value, factorName) {
    // Garantir que o valor está entre 0 e 1
    let normalized = Math.max(0, Math.min(1, value));

    // Aplicar curva de normalização específica por tipo de fator
    if (factorName.includes('goals') || factorName.includes('shots')) {
        // Curva logarítmica para estatísticas de gols
        normalized = Math.log(1 + normalized * 9) / Math.log(10);
    } else if (factorName.includes('cards') || factorName.includes('fouls')) {
        // Curva inversa para estatísticas negativas
        normalized = 1 - normalized;
    }

    return normalized;
}

// Obter dados médicos (simulado - seria integrado com sistema médico real)
async function getMedicalDataFactor(match, endpoint, factorName) {
    // Em um sistema real, isso se conectaria com dados médicos dos jogadores
    // Por enquanto, retorna valores simulados baseados em padrões realistas

    const medicalFactors = {
        'vo2_max': () => 0.7 + Math.random() * 0.2, // 70-90% da capacidade máxima
        'anaerobic_threshold': () => 0.6 + Math.random() * 0.3,
        'body_fat_percentage': () => 0.8 + Math.random() * 0.15, // Baixo % de gordura é bom
        'muscle_mass': () => 0.7 + Math.random() * 0.2,
        'recovery_capacity': () => 0.6 + Math.random() * 0.3,
        'previous_injuries_knee': () => Math.random() * 0.3, // Lesões são negativas
        'previous_injuries_ankle': () => Math.random() * 0.25,
        'injury_recovery_time': () => 0.7 + Math.random() * 0.2,
        'cardiovascular_endurance': () => 0.75 + Math.random() * 0.2,
        'explosive_strength': () => 0.7 + Math.random() * 0.25
    };

    const calculator = medicalFactors[factorName];
    return calculator ? calculator() : 0.5 + (Math.random() - 0.5) * 0.4;
}

// Obter dados de tracking (simulado - seria integrado com sistema de tracking real)
async function getTrackingDataFactor(match, endpoint, factorName) {
    // Em um sistema real, isso se conectaria com dados de GPS/tracking dos jogadores

    const trackingFactors = {
        'total_distance': () => 0.7 + Math.random() * 0.2,
        'max_speed': () => 0.75 + Math.random() * 0.2,
        'sprints_completed': () => 0.6 + Math.random() * 0.3,
        'running_intensity': () => 0.65 + Math.random() * 0.25,
        'physical_duels_won': () => 0.5 + Math.random() * 0.4,
        'acceleration': () => 0.7 + Math.random() * 0.25,
        'deceleration': () => 0.7 + Math.random() * 0.2
    };

    const calculator = trackingFactors[factorName];
    return calculator ? calculator() : 0.5 + (Math.random() - 0.5) * 0.3;
}

// Obter dados biomecânicos (simulado)
async function getBiomechanicsDataFactor(match, endpoint, factorName) {
    const biomechanicsFactors = {
        'running_pattern': () => 0.75 + Math.random() * 0.2,
        'kicking_technique': () => 0.7 + Math.random() * 0.25,
        'heading_mechanics': () => 0.65 + Math.random() * 0.3,
        'body_posture': () => 0.8 + Math.random() * 0.15,
        'joint_alignment': () => 0.75 + Math.random() * 0.2,
        'energy_efficiency': () => 0.7 + Math.random() * 0.25,
        'movement_economy': () => 0.72 + Math.random() * 0.23,
        'core_strength': () => 0.75 + Math.random() * 0.2
    };

    const calculator = biomechanicsFactors[factorName];
    return calculator ? calculator() : 0.6 + Math.random() * 0.3;
}

// Obter dados de análise avançada (simulado)
async function getAdvancedAnalysisFactor(match, endpoint, factorName) {
    const advancedFactors = {
        'movement_off_ball': () => 0.6 + Math.random() * 0.3,
        'defensive_coverage': () => 0.65 + Math.random() * 0.25,
        'individual_marking': () => 0.7 + Math.random() * 0.2,
        'zone_marking': () => 0.68 + Math.random() * 0.22,
        'progressive_passes': () => 0.55 + Math.random() * 0.35,
        'vertical_passes': () => 0.6 + Math.random() * 0.3,
        'offensive_transitions': () => 0.65 + Math.random() * 0.25,
        'defensive_transitions': () => 0.62 + Math.random() * 0.28,
        'positional_play': () => 0.7 + Math.random() * 0.2,
        'spatial_occupation': () => 0.68 + Math.random() * 0.22
    };

    const calculator = advancedFactors[factorName];
    return calculator ? calculator() : 0.55 + Math.random() * 0.35;
}

// Calcular confiança avançada
function calculateAdvancedConfidence(detailedFactors, profile) {
    let totalWeightedValue = 0;
    let totalWeight = 0;
    let reliabilitySum = 0;
    let factorCount = 0;

    for (const category of Object.values(detailedFactors)) {
        for (const subcategory of Object.values(category)) {
            for (const factor of Object.values(subcategory)) {
                if (typeof factor === 'object' && factor.weighted_value !== undefined) {
                    totalWeightedValue += factor.weighted_value;
                    totalWeight += Math.abs(factor.weight);
                    reliabilitySum += factor.reliability || 0.5;
                    factorCount++;
                }
            }
        }
    }

    if (totalWeight === 0 || factorCount === 0) {
        return 50; // Confiança neutra
    }

    // Calcular confiança base
    const baseConfidence = (totalWeightedValue / totalWeight) * 100;

    // Ajustar pela confiabilidade média dos dados
    const avgReliability = reliabilitySum / factorCount;
    const reliabilityAdjustment = avgReliability * 0.2; // Até 20% de ajuste

    // Ajustar pelo número de fatores analisados
    const factorCountBonus = Math.min(factorCount / 100, 0.1); // Até 10% de bônus

    // Calcular confiança final
    let finalConfidence = baseConfidence + (reliabilityAdjustment * 100) + (factorCountBonus * 100);

    // Aplicar limites do perfil
    const profileConfig = getBettingProfileConfig ? getBettingProfileConfig(profile?.profile_type) : null;
    if (profileConfig) {
        finalConfidence = Math.max(finalConfidence, profileConfig.min_confidence || 35);
    }

    return Math.max(10, Math.min(95, finalConfidence));
}

// Gerar recomendações avançadas
function generateAdvancedRecommendations(analysis, profile) {
    const recommendations = [];
    const confidence = analysis.confidence_score;

    // Recomendação baseada na confiança
    if (confidence >= 80) {
        recommendations.push({
            type: 'HIGH_CONFIDENCE',
            message: `Análise com ${analysis.factors_analyzed} fatores indica alta probabilidade de sucesso`,
            confidence: confidence,
            action: 'STRONG_BET'
        });
    } else if (confidence >= 65) {
        recommendations.push({
            type: 'MEDIUM_CONFIDENCE',
            message: `Análise moderadamente favorável com ${analysis.factors_analyzed} fatores`,
            confidence: confidence,
            action: 'MODERATE_BET'
        });
    } else if (confidence >= 45) {
        recommendations.push({
            type: 'LOW_CONFIDENCE',
            message: `Análise inconclusiva, considere fatores adicionais`,
            confidence: confidence,
            action: 'SMALL_BET'
        });
    } else {
        recommendations.push({
            type: 'AVOID',
            message: `Análise desfavorável, evitar aposta`,
            confidence: confidence,
            action: 'NO_BET'
        });
    }

    return recommendations;
}

// Avaliar riscos avançados
function assessAdvancedRisks(analysis, profile) {
    return {
        injury_risk: calculateInjuryRisk(analysis.detailed_factors),
        performance_volatility: calculatePerformanceVolatility(analysis.detailed_factors),
        external_factors_risk: calculateExternalFactorsRisk(analysis.detailed_factors),
        overall_risk_score: calculateOverallRisk(analysis.detailed_factors)
    };
}

// Prever resultados avançados
function predictAdvancedOutcomes(analysis, match) {
    const confidence = analysis.confidence_score;

    return {
        most_likely_outcome: confidence > 60 ? 'HOME_WIN' : confidence > 40 ? 'DRAW' : 'AWAY_WIN',
        probability_home: Math.max(0.1, Math.min(0.8, confidence / 100)),
        probability_draw: 0.25 + (Math.random() - 0.5) * 0.1,
        probability_away: Math.max(0.1, Math.min(0.8, (100 - confidence) / 100)),
        expected_goals_home: 1.2 + (confidence / 100) * 1.5,
        expected_goals_away: 1.2 + ((100 - confidence) / 100) * 1.5
    };
}

// Funções auxiliares de cálculo de risco
function calculateInjuryRisk(factors) {
    // Implementar cálculo baseado em fatores médicos e físicos
    return Math.random() * 0.3; // Placeholder
}

function calculatePerformanceVolatility(factors) {
    // Implementar cálculo baseado em consistência histórica
    return Math.random() * 0.4; // Placeholder
}

function calculateExternalFactorsRisk(factors) {
    // Implementar cálculo baseado em fatores ambientais
    return Math.random() * 0.25; // Placeholder
}

function calculateOverallRisk(factors) {
    // Implementar cálculo de risco geral
    return Math.random() * 0.35; // Placeholder
}

// Exportar funções principais
window.performAdvancedAnalysis = performAdvancedAnalysis;
window.SPORT_ANALYSIS_FACTORS = SPORT_ANALYSIS_FACTORS;
window.getFootballApiFactor = getFootballApiFactor;
window.calculateAdvancedConfidence = calculateAdvancedConfidence;
