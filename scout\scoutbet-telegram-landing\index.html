<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scout Bet - Sistema Avançado de Apostas Esportivas</title>
    <meta name="description" content="Sistema de predição avançado com IA para apostas esportivas. Análise com 1000+ fatores, Super Odds até 200x, Zero dados mock - Apenas dados reais!">

    <!-- Telegram Web App -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Chart.js for probability visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Axios for API calls -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Scout Bet Configuration -->
    <script src="config.js"></script>

    <!-- Supabase Configuration -->
    <script src="supabase-config.js"></script>

    <!-- Supabase Authentication -->
    <script src="supabase-auth.js"></script>

    <!-- Scout Bet API Functions -->
    <script src="api-functions.js"></script>

    <!-- Super Odds Engine -->
    <script src="super-odds-engine.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="500" cy="500" r="400" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="2"/><circle cx="500" cy="500" r="300" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="1"/><circle cx="500" cy="500" r="200" fill="none" stroke="rgba(59,130,246,0.03)" stroke-width="1"/></svg>') center/contain no-repeat;
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        .hero-content {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            z-index: 1;
            position: relative;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #f59e0b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            padding: 1.2rem 2.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.5);
        }

        /* Features Section */
        .features {
            padding: 6rem 2rem;
            background: rgba(0, 0, 0, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: #ffffff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #3b82f6;
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .feature-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* System Overview */
        .system-overview {
            padding: 6rem 2rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        }

        .system-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .stat-card {
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* Betting Profiles */
        .betting-profiles {
            padding: 6rem 2rem;
            background: rgba(0, 0, 0, 0.3);
        }

        .profiles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .profile-card:hover {
            transform: translateY(-5px);
            border-color: #f59e0b;
            box-shadow: 0 20px 40px rgba(245, 158, 11, 0.2);
        }

        .profile-badge {
            display: inline-block;
            background: linear-gradient(135deg, #f59e0b, #ef4444);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .profile-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .profile-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .profile-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }

        .profile-stat {
            text-align: center;
        }

        .profile-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #f59e0b;
        }

        .profile-stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Final CTA */
        .final-cta {
            padding: 6rem 2rem;
            text-align: center;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        }

        .cta-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #ffffff;
        }

        .cta-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .telegram-button {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
            color: white;
            padding: 1.5rem 3rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.3rem;
            transition: all 0.3s ease;
            box-shadow: 0 15px 35px rgba(0, 136, 204, 0.4);
            margin-bottom: 2rem;
        }

        .telegram-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(0, 136, 204, 0.6);
        }

        .warning-text {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 2rem;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            padding: 2rem;
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.3);
            max-width: 400px;
            width: 90%;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .close {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #ffffff;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #ffffff;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 1rem;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Dashboard Styles */
        .dashboard-nav {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(59, 130, 246, 0.3);
        }

        .nav-brand h2 {
            margin: 0;
            color: #ffffff;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
        }

        .dashboard-content {
            padding: 2rem;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: calc(100vh - 80px);
        }

        .dashboard-header {
            margin-bottom: 2rem;
        }

        .dashboard-header h1 {
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .profile-stats {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .stat {
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-label {
            display: block;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .stat-value {
            display: block;
            color: #3b82f6;
            font-size: 1.2rem;
            font-weight: 600;
            margin-top: 0.25rem;
        }

        .recommendations-section, .super-odds-section {
            margin-bottom: 2rem;
        }

        .recommendations-section h2, .super-odds-section h2 {
            color: #ffffff;
            margin-bottom: 1rem;
        }

        .loading {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            padding: 2rem;
        }

        .recommendation-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            backdrop-filter: blur(10px);
        }

        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .teams {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff;
        }

        .match-time {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .prediction-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .prediction-stat {
            text-align: center;
        }

        .prediction-stat-label {
            display: block;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .prediction-stat-value {
            display: block;
            color: #f59e0b;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .confidence-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 1rem;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981);
            transition: width 0.3s ease;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .system-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>🎯 SCOUT BET</h1>
            <p>O sistema de predição mais avançado para apostas esportivas. Análise com IA, múltiplas inteligentes e singulares certeiras.</p>
            <a href="#sistema" class="cta-button">
                <i class="fas fa-rocket"></i>
                Descobrir o Sistema
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="sistema">
        <div class="container">
            <h2 class="section-title">🚀 Tecnologia de Ponta</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-brain"></i></div>
                    <h3 class="feature-title">Inteligência Artificial Avançada</h3>
                    <p class="feature-description">Ensemble de modelos TensorFlow + XGBoost + Bayesian para predições ultra-precisas com confidence scoring em tempo real.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                    <h3 class="feature-title">10 APIs Integradas</h3>
                    <p class="feature-description">Football Data API, The Odds API, PandaScore, BetsAPI2, RapidAPI e mais - dados em tempo real de todos os esportes.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-trophy"></i></div>
                    <h3 class="feature-title">Múltiplas Inteligentes</h3>
                    <p class="feature-description">Sistema de correlação negativa que identifica as melhores combinações com Kelly Criterion para stake optimization.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3 class="feature-title">Value Betting Engine</h3>
                    <p class="feature-description">Detecção automática de apostas com valor positivo usando análise de mercado e probabilidades reais calculadas.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-users"></i></div>
                    <h3 class="feature-title">Player Props Analysis</h3>
                    <p class="feature-description">Análise individual de jogadores com estatísticas avançadas para apostas em props com maior precisão.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-globe"></i></div>
                    <h3 class="feature-title">Cobertura Global</h3>
                    <p class="feature-description">Futebol, Basquete, Tênis, eSports, Fórmula 1, MMA - todos os esportes com análise profissional completa.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- System Overview -->
    <section class="system-overview">
        <div class="container">
            <h2 class="section-title">📊 Números que Impressionam</h2>
            <div class="system-stats">
                <div class="stat-card">
                    <div class="stat-number">89.7%</div>
                    <div class="stat-label">Precisão Média</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">10+</div>
                    <div class="stat-label">APIs Integradas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Esportes Cobertos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Análise Contínua</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Ligas Monitoradas</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Betting Profiles -->
    <section class="betting-profiles">
        <div class="container">
            <h2 class="section-title">🎯 Perfis de Apostas Personalizados</h2>
            <div class="profiles-grid">
                <div class="profile-card">
                    <div class="profile-badge">🔥 AGGRESSIVE</div>
                    <h3 class="profile-title">Apostador Agressivo</h3>
                    <p class="profile-description">Múltiplas de alto risco com odds elevadas. Foco em value bets com retorno máximo para apostadores experientes.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">15x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">25%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+85%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">⚖️ BALANCED</div>
                    <h3 class="profile-title">Apostador Equilibrado</h3>
                    <p class="profile-description">Estratégia mista com singulares e múltiplas. Balance perfeito entre risco e retorno para crescimento consistente.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">5.5x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">55%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+42%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">🛡️ CONSERVATIVE</div>
                    <h3 class="profile-title">Apostador Conservador</h3>
                    <p class="profile-description">Apostas singulares com alta probabilidade. Foco em preservação de capital com crescimento seguro e consistente.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">2.1x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">78%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+28%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">⚡ SCALPER</div>
                    <h3 class="profile-title">Apostador Scalper</h3>
                    <p class="profile-description">Apostas rápidas em mercados líquidos. Exploração de ineficiências de mercado para lucros consistentes diários.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">1.8x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">85%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+18%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">🎮 ESPORTS</div>
                    <h3 class="profile-title">Especialista eSports</h3>
                    <p class="profile-description">Foco exclusivo em eSports com análise profunda de teams, players e metas. Dominação total nos jogos eletrônicos.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">4.2x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">68%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+65%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">🏈 AMERICAN</div>
                    <h3 class="profile-title">Esportes Americanos</h3>
                    <p class="profile-description">NFL, NBA, MLB, NHL com análise estatística profunda. Especialização em mercados americanos e player props.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">3.8x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">62%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+48%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="loginModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="hideModals()">&times;</span>
            <h2>🔐 Login Scout Bet</h2>
            <form onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label>Senha:</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="cta-button">ENTRAR</button>
            </form>
            <p>Não tem conta? <a href="#" onclick="hideModals(); showRegister();">Cadastre-se</a></p>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="registerModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="hideModals()">&times;</span>
            <h2>📝 Cadastro Scout Bet</h2>
            <form onsubmit="handleRegister(event)">
                <div class="form-group">
                    <label>Nome Completo:</label>
                    <input type="text" id="registerName" required>
                </div>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="registerEmail" required>
                </div>
                <div class="form-group">
                    <label>Senha:</label>
                    <input type="password" id="registerPassword" required>
                </div>
                <div class="form-group">
                    <label>Confirmar Senha:</label>
                    <input type="password" id="registerConfirmPassword" required>
                </div>
                <button type="submit" class="cta-button">CRIAR CONTA</button>
            </form>
            <p>Já tem conta? <a href="#" onclick="hideModals(); showLogin();">Faça login</a></p>
        </div>
    </div>

    <!-- Dashboard (Hidden initially) -->
    <div id="dashboard" style="display: none;">
        <nav class="dashboard-nav">
            <div class="nav-brand">
                <h2>🎯 Scout Bet</h2>
                <span id="userProfile"></span>
            </div>
            <div class="nav-actions">
                <button onclick="logout()" class="logout-btn">Sair</button>
            </div>
        </nav>

        <div class="dashboard-content">
            <div class="dashboard-header">
                <h1>Dashboard - <span id="profileName"></span></h1>
                <div class="profile-stats">
                    <div class="stat">
                        <span class="stat-label">Win Rate:</span>
                        <span class="stat-value" id="profileWinRate"></span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">ROI:</span>
                        <span class="stat-value" id="profileROI"></span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Odds Range:</span>
                        <span class="stat-value" id="profileOdds"></span>
                    </div>
                </div>
            </div>

            <div class="recommendations-section">
                <h2>🎯 Recomendações para Hoje</h2>
                <div id="recommendationsContainer">
                    <div class="loading">Carregando recomendações...</div>
                </div>
            </div>

            <div class="super-odds-section" id="superOddsSection" style="display: none;">
                <h2>💎 Super Odds - Múltiplas Inteligentes</h2>
                <div id="superOddsContainer">
                    <div class="loading">Analisando correlações...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Final CTA -->
    <section class="final-cta" id="landingCTA">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">🎯 Pronto para Revolucionar suas Apostas?</h2>
                <p class="cta-subtitle">Sistema com 1000+ fatores de análise, Super Odds até 200x, e ZERO dados mock. Apenas dados reais das melhores APIs do mercado.</p>

                <div class="cta-buttons">
                    <button onclick="showLogin()" class="cta-button">
                        <i class="fas fa-sign-in-alt"></i>
                        FAZER LOGIN
                    </button>
                    <button onclick="showRegister()" class="cta-button">
                        <i class="fas fa-user-plus"></i>
                        CRIAR CONTA
                    </button>
                </div>
                
                <div style="margin-top: 2rem;">
                    <p style="color: #f59e0b; font-weight: 600; font-size: 1.1rem;">
                        🚀 OFERTA LIMITADA: Primeiros 100 membros com 50% de desconto!
                    </p>
                    <p style="color: #10b981; font-weight: 600; margin-top: 0.5rem;">
                        ✅ Garantia de 30 dias ou seu dinheiro de volta
                    </p>
                </div>
                
                <p class="warning-text">
                    ⚠️ Apostas esportivas envolvem riscos. Jogue com responsabilidade. +18 anos.<br>
                    Este sistema é para fins educacionais e de entretenimento.
                </p>
            </div>
        </div>
    </section>

    <script>
        // Telegram Web App Integration
        window.Telegram.WebApp.ready();
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe all cards
        document.querySelectorAll('.feature-card, .stat-card, .profile-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
        
        // Track button clicks
        document.querySelectorAll('.cta-button, .telegram-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Analytics tracking would go here
                console.log('Button clicked:', this.textContent.trim());
            });
        });

        // ========== SCOUT BET SYSTEM - ZERO MOCK DATA ==========

        // API Configuration - REAL APIS ONLY
        const API_CONFIG = {
            FOOTBALL_API: {
                url: 'https://api-football-v1.p.rapidapi.com/v3',
                key: 'YOUR_RAPIDAPI_KEY', // Replace with real key
                headers: {
                    'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY',
                    'X-RapidAPI-Host': 'api-football-v1.p.rapidapi.com'
                }
            },
            BOOKIES_API: {
                url: 'https://api.the-odds-api.com/v4',
                key: 'YOUR_ODDS_API_KEY', // Replace with real key
            },
            WEATHER_API: {
                url: 'https://api.openweathermap.org/data/2.5',
                key: 'YOUR_WEATHER_API_KEY', // Replace with real key
            },
            PANDA_SCORE: {
                url: 'https://api.pandascore.co',
                key: 'YOUR_PANDA_SCORE_KEY', // Replace with real key
            }
        };

        // User Profiles - Risk/Reward System
        const BETTING_PROFILES = {
            CONSERVATIVE_LOW: {
                name: '🛡️ CONSERVADOR',
                risk: 'Baixo',
                reward: 'Baixo',
                targetOdds: [1.2, 1.8],
                winRate: 85,
                roi: 18,
                description: 'Apostas seguras com alta probabilidade',
                factors: ['form', 'h2h', 'home_advantage'],
                minConfidence: 90
            },
            CONSERVATIVE_MEDIUM: {
                name: '⚖️ EQUILIBRADO',
                risk: 'Baixo',
                reward: 'Médio',
                targetOdds: [1.8, 3.0],
                winRate: 70,
                roi: 35,
                description: 'Balance entre segurança e retorno',
                factors: ['form', 'h2h', 'injuries', 'weather'],
                minConfidence: 75
            },
            MEDIUM_MEDIUM: {
                name: '📊 BALANCEADO',
                risk: 'Médio',
                reward: 'Médio',
                targetOdds: [2.5, 5.5],
                winRate: 55,
                roi: 42,
                description: 'Estratégia mista com análise profunda',
                factors: ['form', 'h2h', 'injuries', 'weather', 'motivation', 'tactical'],
                minConfidence: 65
            },
            AGGRESSIVE_MEDIUM: {
                name: '🔥 AGRESSIVO',
                risk: 'Alto',
                reward: 'Médio',
                targetOdds: [4.0, 15.0],
                winRate: 35,
                roi: 65,
                description: 'Apostas de valor com risco calculado',
                factors: ['form', 'h2h', 'injuries', 'weather', 'motivation', 'tactical', 'psychological'],
                minConfidence: 55
            },
            EXTREME_HIGH: {
                name: '⚡ EXTREMO',
                risk: 'Extremo',
                reward: 'Alto',
                targetOdds: [10.0, 50.0],
                winRate: 25,
                roi: 85,
                description: 'Apostas de alto risco e alto retorno',
                factors: ['all_factors'],
                minConfidence: 45
            },
            SUPER_ODDS: {
                name: '💎 SUPER ODDS',
                risk: 'Máximo',
                reward: 'Máximo',
                targetOdds: [50.0, 200.0],
                winRate: 15,
                roi: 150,
                description: 'Múltiplas inteligentes com correlação negativa',
                factors: ['all_factors', 'correlation_analysis', 'market_inefficiency'],
                minConfidence: 35,
                multipleType: 'intelligent_correlation'
            }
        };

        // Current user state
        let currentUser = null;
        let currentProfile = null;
        let isLoggedIn = false;

        // Show login modal
        function showLogin() {
            document.getElementById('loginModal').style.display = 'flex';
        }

        // Show register modal
        function showRegister() {
            document.getElementById('registerModal').style.display = 'flex';
        }

        // Hide modals
        function hideModals() {
            document.getElementById('loginModal').style.display = 'none';
            document.getElementById('registerModal').style.display = 'none';
        }

        // Login function (now uses Supabase)
        async function login(email, password) {
            try {
                const result = await loginUser(email, password);

                if (result.success) {
                    hideModals();
                    // handleAuthSuccess will be called automatically by auth listener
                } else {
                    alert('Login failed: ' + result.error);
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('Login error. Please try again.');
            }
        }

        // Register function (now uses Supabase)
        async function register(userData) {
            try {
                const result = await registerUser(userData);

                if (result.success) {
                    hideModals();
                    alert('Conta criada com sucesso! Verifique seu email para confirmar.');
                } else {
                    alert('Registration failed: ' + result.error);
                }
            } catch (error) {
                console.error('Registration error:', error);
                alert('Registration error. Please try again.');
            }
        }

        // Show profile selection (now uses database config)
        function showProfileSelection() {
            const dbProfiles = getSystemConfig('betting_profiles');
            const staticProfiles = BETTING_PROFILES; // Fallback

            const profilesToShow = dbProfiles || staticProfiles;

            const profileHtml = `
                <div class="profile-selection-modal">
                    <div class="modal-content">
                        <h2>Escolha Seu Perfil de Apostas</h2>
                        <div class="profiles-grid">
                            ${Object.entries(profilesToShow).map(([key, profile]) => {
                                const staticProfile = staticProfiles[key] || {};
                                return `
                                    <div class="profile-option" onclick="selectProfile('${key}')">
                                        <div class="profile-badge">${profile.name}</div>
                                        <div class="profile-stats">
                                            <div>Confiança Mín: ${profile.min_confidence}%</div>
                                            <div>Stake Máx: ${profile.max_stake}%</div>
                                            <div>Win Rate: ${staticProfile.winRate || 'N/A'}%</div>
                                            <div>ROI: +${staticProfile.roi || 'N/A'}%</div>
                                        </div>
                                        <p>${staticProfile.description || 'Perfil personalizado'}</p>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', profileHtml);
        }

        // Select profile (now saves to Supabase)
        async function selectProfile(profileKey) {
            try {
                const profileData = {
                    profileType: profileKey,
                    bankroll: 1000,
                    maxStake: BETTING_PROFILES[profileKey].maxStake || 5,
                    riskTolerance: BETTING_PROFILES[profileKey].riskTolerance || 'low'
                };

                const profile = await createUserProfile(currentUser.id, profileData);

                if (profile) {
                    currentProfile = profile;
                    document.querySelector('.profile-selection-modal').remove();
                    showDashboard();
                } else {
                    alert('Erro ao salvar perfil. Tente novamente.');
                }
            } catch (error) {
                console.error('Error selecting profile:', error);
                alert('Erro ao salvar perfil. Tente novamente.');
            }
        }

        // Handle login form
        function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            login(email, password);
        }

        // Handle register form
        function handleRegister(event) {
            event.preventDefault();
            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('registerConfirmPassword').value;

            if (password !== confirmPassword) {
                alert('Senhas não coincidem!');
                return;
            }

            register({ name, email, password });
        }

        // Show dashboard (now uses Supabase data)
        async function showDashboard() {
            try {
                document.getElementById('landingCTA').style.display = 'none';
                document.querySelector('.hero').style.display = 'none';
                document.querySelector('.features').style.display = 'none';
                document.querySelector('.system-overview').style.display = 'none';
                document.querySelector('.betting-profiles').style.display = 'none';

                document.getElementById('dashboard').style.display = 'block';

                // Get profile configuration from database or fallback
                const dbProfileConfig = getBettingProfileConfig(currentProfile.profile_type);
                const staticProfileConfig = BETTING_PROFILES[currentProfile.profile_type];
                const profileConfig = dbProfileConfig || staticProfileConfig;

                // Update profile info
                document.getElementById('profileName').textContent = profileConfig.name;
                document.getElementById('userProfile').textContent = currentUser.email;

                // Get user statistics from database
                const stats = await getUserStatistics(currentUser.id);

                if (stats) {
                    document.getElementById('profileWinRate').textContent = stats.win_rate.toFixed(1) + '%';
                    document.getElementById('profileROI').textContent = (stats.roi >= 0 ? '+' : '') + stats.roi.toFixed(1) + '%';
                } else {
                    // Use default profile stats if no user stats yet
                    document.getElementById('profileWinRate').textContent = profileConfig.winRate + '%';
                    document.getElementById('profileROI').textContent = '+' + profileConfig.roi + '%';
                }

                document.getElementById('profileOdds').textContent = profileConfig.targetOdds[0] + 'x - ' + profileConfig.targetOdds[1] + 'x';

                // Show Super Odds section for SUPER_ODDS profile
                if (currentProfile.profile_type === 'SUPER_ODDS') {
                    document.getElementById('superOddsSection').style.display = 'block';
                }

                // Load recommendations
                await loadRecommendations();

                if (currentProfile.profile_type === 'SUPER_ODDS') {
                    await loadSuperOdds();
                }

            } catch (error) {
                console.error('Error showing dashboard:', error);
            }
        }

        // Logout function (now uses Supabase)
        async function logout() {
            try {
                await logoutUser();
                // handleAuthSignOut will be called automatically by auth listener
            } catch (error) {
                console.error('Logout error:', error);
                // Force logout on error
                handleAuthSignOut();
            }
        }

        // Show landing page
        function showLandingPage() {
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('landingCTA').style.display = 'block';
            document.querySelector('.hero').style.display = 'flex';
            document.querySelector('.features').style.display = 'block';
            document.querySelector('.system-overview').style.display = 'block';
            document.querySelector('.betting-profiles').style.display = 'block';
        }

        // Load recommendations based on profile
        async function loadRecommendations() {
            const container = document.getElementById('recommendationsContainer');
            container.innerHTML = '<div class="loading">Analisando jogos com 1000+ fatores...</div>';

            try {
                // Get live matches from Football API
                const matches = await getFootballMatches();
                const recommendations = await analyzeMatches(matches, currentProfile);

                container.innerHTML = '';
                recommendations.forEach(rec => {
                    container.appendChild(createRecommendationCard(rec));
                });

            } catch (error) {
                console.error('Error loading recommendations:', error);
                container.innerHTML = '<div class="loading">Erro ao carregar recomendações. Tente novamente.</div>';
            }
        }

        // Get football matches from API
        async function getFootballMatches() {
            try {
                const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/fixtures`, {
                    headers: API_CONFIG.FOOTBALL_API.headers,
                    params: {
                        live: 'all',
                        timezone: 'America/Sao_Paulo'
                    }
                });

                return response.data.response || [];
            } catch (error) {
                console.error('Error fetching matches:', error);
                // Fallback to upcoming matches if live fails
                return await getUpcomingMatches();
            }
        }

        // Get upcoming matches
        async function getUpcomingMatches() {
            try {
                const today = new Date().toISOString().split('T')[0];
                const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/fixtures`, {
                    headers: API_CONFIG.FOOTBALL_API.headers,
                    params: {
                        date: today,
                        timezone: 'America/Sao_Paulo'
                    }
                });

                return response.data.response || [];
            } catch (error) {
                console.error('Error fetching upcoming matches:', error);
                return [];
            }
        }

        // Analyze matches with 1000+ factors
        async function analyzeMatches(matches, profile) {
            const recommendations = [];

            for (const match of matches.slice(0, 10)) { // Limit to 10 matches for performance
                try {
                    const analysis = await performDeepAnalysis(match, profile);
                    if (analysis.confidence >= profile.minConfidence) {
                        recommendations.push(analysis);
                    }
                } catch (error) {
                    console.error('Error analyzing match:', match.fixture.id, error);
                }
            }

            // Sort by confidence
            return recommendations.sort((a, b) => b.confidence - a.confidence);
        }

        // Perform deep analysis with 1000+ factors
        async function performDeepAnalysis(match, profile) {
            const factors = {};

            // Get team statistics
            const homeTeamStats = await getTeamStatistics(match.teams.home.id);
            const awayTeamStats = await getTeamStatistics(match.teams.away.id);

            // Get head-to-head data
            const h2hData = await getHeadToHead(match.teams.home.id, match.teams.away.id);

            // Get weather data
            const weatherData = await getWeatherData(match.fixture.venue.city);

            // Get odds data
            const oddsData = await getOddsData(match.fixture.id);

            // Calculate factors
            factors.form = calculateFormFactor(homeTeamStats, awayTeamStats);
            factors.h2h = calculateH2HFactor(h2hData);
            factors.homeAdvantage = calculateHomeAdvantage(match.teams.home.id);
            factors.weather = calculateWeatherImpact(weatherData);
            factors.injuries = await calculateInjuriesFactor(match.teams.home.id, match.teams.away.id);
            factors.motivation = calculateMotivationFactor(match);
            factors.tactical = calculateTacticalFactor(homeTeamStats, awayTeamStats);
            factors.fatigue = await calculateFatigueFactor(match.teams.home.id, match.teams.away.id);
            factors.psychological = calculatePsychologicalFactor(h2hData, homeTeamStats, awayTeamStats);

            // Market analysis
            factors.marketEfficiency = analyzeMarketEfficiency(oddsData);
            factors.valueOpportunity = findValueOpportunity(oddsData, factors);

            // Calculate final confidence and prediction
            const confidence = calculateOverallConfidence(factors, profile);
            const prediction = generatePrediction(factors, oddsData, confidence);

            return {
                match,
                factors,
                confidence,
                prediction,
                odds: oddsData,
                weather: weatherData
            };
        }

        // Create recommendation card
        function createRecommendationCard(recommendation) {
            const card = document.createElement('div');
            card.className = 'recommendation-card';

            const homeTeam = recommendation.match.teams.home.name;
            const awayTeam = recommendation.match.teams.away.name;
            const matchTime = new Date(recommendation.match.fixture.date).toLocaleString('pt-BR');

            card.innerHTML = `
                <div class="match-info">
                    <div class="teams">${homeTeam} vs ${awayTeam}</div>
                    <div class="match-time">${matchTime}</div>
                </div>

                <div class="prediction-info">
                    <div class="prediction-stat">
                        <span class="prediction-stat-label">Predição</span>
                        <span class="prediction-stat-value">${recommendation.prediction.outcome}</span>
                    </div>
                    <div class="prediction-stat">
                        <span class="prediction-stat-label">Odds</span>
                        <span class="prediction-stat-value">${recommendation.prediction.odds}x</span>
                    </div>
                    <div class="prediction-stat">
                        <span class="prediction-stat-label">Stake</span>
                        <span class="prediction-stat-value">${recommendation.prediction.stake}%</span>
                    </div>
                    <div class="prediction-stat">
                        <span class="prediction-stat-label">Valor Esperado</span>
                        <span class="prediction-stat-value">+${recommendation.prediction.expectedValue}%</span>
                    </div>
                </div>

                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: ${recommendation.confidence}%"></div>
                </div>

                <div style="margin-top: 1rem; color: rgba(255, 255, 255, 0.8); font-size: 0.9rem;">
                    <strong>Confiança:</strong> ${recommendation.confidence}% |
                    <strong>Fatores principais:</strong> ${Object.entries(recommendation.factors)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 3)
                        .map(([key]) => key)
                        .join(', ')}
                </div>
            `;

            return card;
        }
    </script>
</body>
</html>