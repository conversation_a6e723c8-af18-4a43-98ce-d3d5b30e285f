<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scout Bet - O Futuro das Apostas Esportivas</title>
    <meta name="description" content="Sistema de predição avançado com IA para apostas esportivas. Análise de múltiplas e singulares com precisão científica. Junte-se ao canal Scout Bet no Telegram!">
    
    <!-- Telegram Web App -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="500" cy="500" r="400" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="2"/><circle cx="500" cy="500" r="300" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="1"/><circle cx="500" cy="500" r="200" fill="none" stroke="rgba(59,130,246,0.03)" stroke-width="1"/></svg>') center/contain no-repeat;
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.8; }
        }

        .hero-content {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            z-index: 1;
            position: relative;
        }

        .hero h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #f59e0b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            padding: 1.2rem 2.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.5);
        }

        /* Features Section */
        .features {
            padding: 6rem 2rem;
            background: rgba(0, 0, 0, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: #ffffff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #3b82f6;
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .feature-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* System Overview */
        .system-overview {
            padding: 6rem 2rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
        }

        .system-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .stat-card {
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* Betting Profiles */
        .betting-profiles {
            padding: 6rem 2rem;
            background: rgba(0, 0, 0, 0.3);
        }

        .profiles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .profile-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .profile-card:hover {
            transform: translateY(-5px);
            border-color: #f59e0b;
            box-shadow: 0 20px 40px rgba(245, 158, 11, 0.2);
        }

        .profile-badge {
            display: inline-block;
            background: linear-gradient(135deg, #f59e0b, #ef4444);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .profile-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .profile-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .profile-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 1rem;
        }

        .profile-stat {
            text-align: center;
        }

        .profile-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #f59e0b;
        }

        .profile-stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Final CTA */
        .final-cta {
            padding: 6rem 2rem;
            text-align: center;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        }

        .cta-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #ffffff;
        }

        .cta-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .telegram-button {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            background: linear-gradient(135deg, #0088cc 0%, #229ed9 100%);
            color: white;
            padding: 1.5rem 3rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.3rem;
            transition: all 0.3s ease;
            box-shadow: 0 15px 35px rgba(0, 136, 204, 0.4);
            margin-bottom: 2rem;
        }

        .telegram-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(0, 136, 204, 0.6);
        }

        .warning-text {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 2rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .system-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>🎯 SCOUT BET</h1>
            <p>O sistema de predição mais avançado para apostas esportivas. Análise com IA, múltiplas inteligentes e singulares certeiras.</p>
            <a href="#sistema" class="cta-button">
                <i class="fas fa-rocket"></i>
                Descobrir o Sistema
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="sistema">
        <div class="container">
            <h2 class="section-title">🚀 Tecnologia de Ponta</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-brain"></i></div>
                    <h3 class="feature-title">Inteligência Artificial Avançada</h3>
                    <p class="feature-description">Ensemble de modelos TensorFlow + XGBoost + Bayesian para predições ultra-precisas com confidence scoring em tempo real.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                    <h3 class="feature-title">10 APIs Integradas</h3>
                    <p class="feature-description">Football Data API, The Odds API, PandaScore, BetsAPI2, RapidAPI e mais - dados em tempo real de todos os esportes.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-trophy"></i></div>
                    <h3 class="feature-title">Múltiplas Inteligentes</h3>
                    <p class="feature-description">Sistema de correlação negativa que identifica as melhores combinações com Kelly Criterion para stake optimization.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3 class="feature-title">Value Betting Engine</h3>
                    <p class="feature-description">Detecção automática de apostas com valor positivo usando análise de mercado e probabilidades reais calculadas.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-users"></i></div>
                    <h3 class="feature-title">Player Props Analysis</h3>
                    <p class="feature-description">Análise individual de jogadores com estatísticas avançadas para apostas em props com maior precisão.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-globe"></i></div>
                    <h3 class="feature-title">Cobertura Global</h3>
                    <p class="feature-description">Futebol, Basquete, Tênis, eSports, Fórmula 1, MMA - todos os esportes com análise profissional completa.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- System Overview -->
    <section class="system-overview">
        <div class="container">
            <h2 class="section-title">📊 Números que Impressionam</h2>
            <div class="system-stats">
                <div class="stat-card">
                    <div class="stat-number">89.7%</div>
                    <div class="stat-label">Precisão Média</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">10+</div>
                    <div class="stat-label">APIs Integradas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Esportes Cobertos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Análise Contínua</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Ligas Monitoradas</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Betting Profiles -->
    <section class="betting-profiles">
        <div class="container">
            <h2 class="section-title">🎯 Perfis de Apostas Personalizados</h2>
            <div class="profiles-grid">
                <div class="profile-card">
                    <div class="profile-badge">🔥 AGGRESSIVE</div>
                    <h3 class="profile-title">Apostador Agressivo</h3>
                    <p class="profile-description">Múltiplas de alto risco com odds elevadas. Foco em value bets com retorno máximo para apostadores experientes.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">15x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">25%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+85%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">⚖️ BALANCED</div>
                    <h3 class="profile-title">Apostador Equilibrado</h3>
                    <p class="profile-description">Estratégia mista com singulares e múltiplas. Balance perfeito entre risco e retorno para crescimento consistente.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">5.5x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">55%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+42%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">🛡️ CONSERVATIVE</div>
                    <h3 class="profile-title">Apostador Conservador</h3>
                    <p class="profile-description">Apostas singulares com alta probabilidade. Foco em preservação de capital com crescimento seguro e consistente.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">2.1x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">78%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+28%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">⚡ SCALPER</div>
                    <h3 class="profile-title">Apostador Scalper</h3>
                    <p class="profile-description">Apostas rápidas em mercados líquidos. Exploração de ineficiências de mercado para lucros consistentes diários.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">1.8x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">85%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+18%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">🎮 ESPORTS</div>
                    <h3 class="profile-title">Especialista eSports</h3>
                    <p class="profile-description">Foco exclusivo em eSports com análise profunda de teams, players e metas. Dominação total nos jogos eletrônicos.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">4.2x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">68%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+65%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
                
                <div class="profile-card">
                    <div class="profile-badge">🏈 AMERICAN</div>
                    <h3 class="profile-title">Esportes Americanos</h3>
                    <p class="profile-description">NFL, NBA, MLB, NHL com análise estatística profunda. Especialização em mercados americanos e player props.</p>
                    <div class="profile-stats">
                        <div class="profile-stat">
                            <div class="profile-stat-value">3.8x</div>
                            <div class="profile-stat-label">Odds Médias</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">62%</div>
                            <div class="profile-stat-label">Win Rate</div>
                        </div>
                        <div class="profile-stat">
                            <div class="profile-stat-value">+48%</div>
                            <div class="profile-stat-label">ROI</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="final-cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">🎯 Pronto para Revolucionar suas Apostas?</h2>
                <p class="cta-subtitle">Junte-se aos milhares de apostadores que já estão lucrando com o sistema mais avançado do mercado. Acesso VIP com predições diárias, múltiplas inteligentes e suporte 24/7.</p>
                
                <a href="https://t.me/scoutbet_oficial" class="telegram-button" target="_blank">
                    <i class="fab fa-telegram-plane"></i>
                    ENTRAR NO CANAL VIP
                </a>
                
                <div style="margin-top: 2rem;">
                    <p style="color: #f59e0b; font-weight: 600; font-size: 1.1rem;">
                        🚀 OFERTA LIMITADA: Primeiros 100 membros com 50% de desconto!
                    </p>
                    <p style="color: #10b981; font-weight: 600; margin-top: 0.5rem;">
                        ✅ Garantia de 30 dias ou seu dinheiro de volta
                    </p>
                </div>
                
                <p class="warning-text">
                    ⚠️ Apostas esportivas envolvem riscos. Jogue com responsabilidade. +18 anos.<br>
                    Este sistema é para fins educacionais e de entretenimento.
                </p>
            </div>
        </div>
    </section>

    <script>
        // Telegram Web App Integration
        window.Telegram.WebApp.ready();
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe all cards
        document.querySelectorAll('.feature-card, .stat-card, .profile-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
        
        // Track button clicks
        document.querySelectorAll('.cta-button, .telegram-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Analytics tracking would go here
                console.log('Button clicked:', this.textContent.trim());
            });
        });
    </script>
</body>
</html>