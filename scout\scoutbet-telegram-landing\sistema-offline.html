<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Scout Bet - Sistema Offline</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
        }
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .profile-card {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid #3b82f6;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .recommendation {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .team {
            font-weight: 600;
            font-size: 1.1rem;
        }
        .vs {
            color: #6b7280;
            font-weight: 500;
        }
        .prediction {
            background: rgba(0, 0, 0, 0.2);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .confidence {
            font-size: 1.2rem;
            font-weight: 700;
            color: #10b981;
        }
        .factors {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.5rem;
            margin: 1rem 0;
        }
        .factor {
            background: rgba(255, 255, 255, 0.05);
            padding: 0.5rem;
            border-radius: 6px;
            text-align: center;
            font-size: 0.9rem;
        }
        .button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .success {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #6ee7b7;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 Scout Bet</h1>
            <p class="subtitle">Sistema de Análise Avançada - Versão Offline</p>
            
            <div class="success">
                <strong>✅ SISTEMA FUNCIONANDO!</strong><br>
                Esta versão funciona sem banco de dados e mostra como seria o sistema completo.
            </div>
        </div>

        <div class="dashboard">
            <!-- Sidebar -->
            <div class="sidebar">
                <h3>👤 Perfil do Usuário</h3>
                <div class="profile-card">
                    <h4>📊 BALANCEADO</h4>
                    <p><strong>Confiança Mínima:</strong> 65%</p>
                    <p><strong>Stake Máximo:</strong> 10%</p>
                    <p><strong>Win Rate:</strong> 68.5%</p>
                    <p><strong>ROI:</strong> +42.3%</p>
                </div>

                <h3>📊 Estatísticas</h3>
                <div style="background: rgba(255, 255, 255, 0.03); padding: 1rem; border-radius: 8px;">
                    <p><strong>Total de Apostas:</strong> 127</p>
                    <p><strong>Apostas Ganhas:</strong> 87</p>
                    <p><strong>Total Apostado:</strong> R$ 2.540</p>
                    <p><strong>Total Ganho:</strong> R$ 3.615</p>
                    <p><strong>Lucro:</strong> <span style="color: #10b981;">+R$ 1.075</span></p>
                </div>

                <button class="button" onclick="gerarNovaAnalise()">🔄 Nova Análise</button>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <h3>🎯 Recomendações Ativas</h3>
                
                <div class="recommendation" id="recommendation1">
                    <div class="match-info">
                        <span class="team">Flamengo</span>
                        <span class="vs">vs</span>
                        <span class="team">Palmeiras</span>
                    </div>
                    
                    <div class="prediction">
                        <p><strong>Predição:</strong> Vitória do Flamengo</p>
                        <p><strong>Odds:</strong> 2.45</p>
                        <p><strong>Stake Recomendado:</strong> 8.5% da banca</p>
                        <p class="confidence">Confiança: 78.3%</p>
                    </div>

                    <p><strong>🧠 Análise Avançada:</strong> 347 fatores analisados</p>
                    
                    <div class="factors">
                        <div class="factor">Forma: 85%</div>
                        <div class="factor">H2H: 72%</div>
                        <div class="factor">Casa: 65%</div>
                        <div class="factor">Lesões: 90%</div>
                        <div class="factor">Tático: 68%</div>
                        <div class="factor">Motivação: 82%</div>
                    </div>

                    <p><strong>💡 Insight:</strong> Flamengo com vantagem significativa em forma recente e sem desfalques importantes. Palmeiras com 3 titulares lesionados.</p>
                </div>

                <div class="recommendation" id="recommendation2">
                    <div class="match-info">
                        <span class="team">Real Madrid</span>
                        <span class="vs">vs</span>
                        <span class="team">Barcelona</span>
                    </div>
                    
                    <div class="prediction">
                        <p><strong>Predição:</strong> Mais de 2.5 gols</p>
                        <p><strong>Odds:</strong> 1.85</p>
                        <p><strong>Stake Recomendado:</strong> 6.2% da banca</p>
                        <p class="confidence">Confiança: 71.8%</p>
                    </div>

                    <p><strong>🧠 Análise Avançada:</strong> 423 fatores analisados</p>
                    
                    <div class="factors">
                        <div class="factor">Ofensiva: 92%</div>
                        <div class="factor">Defensiva: 45%</div>
                        <div class="factor">Histórico: 88%</div>
                        <div class="factor">Clima: 75%</div>
                        <div class="factor">Pressão: 95%</div>
                        <div class="factor">Rivalidade: 100%</div>
                    </div>

                    <p><strong>💡 Insight:</strong> Clássico com histórico de muitos gols. Ambas equipes com ataques potentes e defesas vulneráveis. Pressão máxima por resultado.</p>
                </div>

                <div class="recommendation" style="background: rgba(245, 158, 11, 0.1); border-color: #f59e0b;">
                    <div class="match-info">
                        <span class="team">💎 SUPER ODDS</span>
                        <span class="vs">-</span>
                        <span class="team">Múltipla 47.5x</span>
                    </div>
                    
                    <div class="prediction">
                        <p><strong>Múltipla Inteligente:</strong> 4 jogos correlacionados</p>
                        <p><strong>Odds Total:</strong> 47.5x</p>
                        <p><strong>Stake Recomendado:</strong> 1.8% da banca</p>
                        <p class="confidence">Confiança: 38.7%</p>
                    </div>

                    <p><strong>🧠 Análise Avançada:</strong> 1.247 fatores analisados</p>
                    
                    <div class="factors">
                        <div class="factor">Correlação: -0.73</div>
                        <div class="factor">Diversificação: 95%</div>
                        <div class="factor">Kelly: 1.8%</div>
                        <div class="factor">EV: +127%</div>
                    </div>

                    <p><strong>💡 Insight:</strong> Múltipla com correlação negativa entre eventos. Alto potencial de retorno com risco controlado através de diversificação geográfica.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simular sistema funcionando
        function gerarNovaAnalise() {
            const recommendations = document.querySelectorAll('.recommendation');
            
            recommendations.forEach((rec, index) => {
                rec.style.opacity = '0.5';
                rec.style.transform = 'scale(0.98)';
                
                setTimeout(() => {
                    // Simular nova análise
                    const confidence = rec.querySelector('.confidence');
                    const currentConf = parseFloat(confidence.textContent.match(/[\d.]+/)[0]);
                    const newConf = (currentConf + (Math.random() - 0.5) * 10).toFixed(1);
                    confidence.textContent = `Confiança: ${newConf}%`;
                    
                    // Atualizar fatores
                    const factors = rec.querySelectorAll('.factor');
                    factors.forEach(factor => {
                        const currentValue = parseInt(factor.textContent.match(/\d+/)[0]);
                        const newValue = Math.max(10, Math.min(100, currentValue + Math.floor((Math.random() - 0.5) * 20)));
                        const factorName = factor.textContent.split(':')[0];
                        factor.textContent = `${factorName}: ${newValue}%`;
                    });
                    
                    rec.style.opacity = '1';
                    rec.style.transform = 'scale(1)';
                }, 1000 + index * 200);
            });
            
            // Feedback visual
            const button = event.target;
            button.textContent = '🔄 Analisando...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = '✅ Análise Atualizada';
                setTimeout(() => {
                    button.textContent = '🔄 Nova Análise';
                    button.disabled = false;
                }, 1500);
            }, 2000);
        }

        // Animações iniciais
        document.addEventListener('DOMContentLoaded', function() {
            const recommendations = document.querySelectorAll('.recommendation');
            recommendations.forEach((rec, index) => {
                rec.style.opacity = '0';
                rec.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    rec.style.transition = 'all 0.5s ease';
                    rec.style.opacity = '1';
                    rec.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        console.log('🎯 Scout Bet Sistema Offline carregado!');
        console.log('✅ Todas as funcionalidades simuladas funcionando');
        console.log('🧠 347-1247 fatores sendo analisados por recomendação');
    </script>
</body>
</html>
