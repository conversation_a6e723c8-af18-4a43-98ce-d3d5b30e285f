// ========== SUPER ODDS ENGINE - INTELLIGENT MULTIPLES ==========
// Advanced correlation analysis for 100-200x odds multiples

// Analyze market efficiency
function analyzeMarketEfficiency(oddsData) {
    if (!oddsData || !oddsData.bookmakers) return 0.5;
    
    const bookmakers = oddsData.bookmakers;
    if (bookmakers.length < 2) return 0.5;
    
    // Calculate odds variance across bookmakers
    const homeOdds = bookmakers.map(b => b.bets[0]?.values[0]?.odd || 0).filter(o => o > 0);
    const drawOdds = bookmakers.map(b => b.bets[0]?.values[1]?.odd || 0).filter(o => o > 0);
    const awayOdds = bookmakers.map(b => b.bets[0]?.values[2]?.odd || 0).filter(o => o > 0);
    
    const homeVariance = calculateVariance(homeOdds);
    const drawVariance = calculateVariance(drawOdds);
    const awayVariance = calculateVariance(awayOdds);
    
    // Higher variance = less efficient market = more opportunity
    const avgVariance = (homeVariance + drawVariance + awayVariance) / 3;
    return Math.min(0.9, avgVariance * 10); // Scale to 0-0.9
}

// Calculate variance of odds
function calculateVariance(odds) {
    if (odds.length < 2) return 0;
    
    const mean = odds.reduce((sum, odd) => sum + odd, 0) / odds.length;
    const variance = odds.reduce((sum, odd) => sum + Math.pow(odd - mean, 2), 0) / odds.length;
    
    return variance / (mean * mean); // Coefficient of variation
}

// Find value opportunity
function findValueOpportunity(oddsData, factors) {
    if (!oddsData) return 0;
    
    // Calculate true probability based on our factors
    const trueProbability = calculateTrueProbability(factors);
    
    // Get best available odds
    const bestOdds = getBestOdds(oddsData);
    
    // Calculate expected value
    const impliedProbability = 1 / bestOdds.home;
    const expectedValue = (trueProbability * bestOdds.home) - 1;
    
    return Math.max(0, expectedValue);
}

// Calculate true probability from factors
function calculateTrueProbability(factors) {
    const weights = {
        form: 0.25,
        h2h: 0.15,
        homeAdvantage: 0.15,
        weather: 0.05,
        injuries: 0.15,
        motivation: 0.10,
        tactical: 0.10,
        psychological: 0.05
    };
    
    let weightedSum = 0;
    let totalWeight = 0;
    
    Object.entries(factors).forEach(([factor, value]) => {
        if (weights[factor] && typeof value === 'number') {
            weightedSum += value * weights[factor];
            totalWeight += weights[factor];
        }
    });
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0.5;
}

// Get best odds from bookmakers
function getBestOdds(oddsData) {
    if (!oddsData || !oddsData.bookmakers) {
        return { home: 2.0, draw: 3.0, away: 2.0 };
    }
    
    let bestHome = 0;
    let bestDraw = 0;
    let bestAway = 0;
    
    oddsData.bookmakers.forEach(bookmaker => {
        const mainMarket = bookmaker.bets.find(bet => bet.name === 'Match Winner');
        if (mainMarket && mainMarket.values) {
            const homeOdd = mainMarket.values.find(v => v.value === 'Home')?.odd || 0;
            const drawOdd = mainMarket.values.find(v => v.value === 'Draw')?.odd || 0;
            const awayOdd = mainMarket.values.find(v => v.value === 'Away')?.odd || 0;
            
            if (homeOdd > bestHome) bestHome = homeOdd;
            if (drawOdd > bestDraw) bestDraw = drawOdd;
            if (awayOdd > bestAway) bestAway = awayOdd;
        }
    });
    
    return {
        home: bestHome || 2.0,
        draw: bestDraw || 3.0,
        away: bestAway || 2.0
    };
}

// Calculate overall confidence
function calculateOverallConfidence(factors, profile) {
    const factorWeights = {
        form: 0.20,
        h2h: 0.15,
        homeAdvantage: 0.10,
        weather: 0.05,
        injuries: 0.15,
        motivation: 0.10,
        tactical: 0.15,
        fatigue: 0.05,
        psychological: 0.05
    };
    
    let confidence = 0;
    let totalWeight = 0;
    
    Object.entries(factors).forEach(([factor, value]) => {
        if (factorWeights[factor] && typeof value === 'number') {
            // Convert factor value to confidence contribution
            const contribution = Math.abs(value - 0.5) * 2; // 0-1 scale
            confidence += contribution * factorWeights[factor];
            totalWeight += factorWeights[factor];
        }
    });
    
    // Normalize to percentage
    const baseConfidence = totalWeight > 0 ? (confidence / totalWeight) * 100 : 50;
    
    // Apply profile-specific adjustments
    if (profile.name.includes('CONSERVADOR')) {
        return Math.min(95, baseConfidence * 1.1); // Conservative boost
    } else if (profile.name.includes('EXTREMO')) {
        return Math.max(30, baseConfidence * 0.8); // Extreme penalty
    }
    
    return Math.max(10, Math.min(95, baseConfidence));
}

// Generate prediction
function generatePrediction(factors, oddsData, confidence) {
    const bestOdds = getBestOdds(oddsData);
    
    // Determine most likely outcome
    let outcome = 'Home';
    let odds = bestOdds.home;
    
    if (factors.form < 0.4 && factors.homeAdvantage < 0.5) {
        outcome = 'Away';
        odds = bestOdds.away;
    } else if (factors.form > 0.4 && factors.form < 0.6 && bestOdds.draw > 3.0) {
        outcome = 'Draw';
        odds = bestOdds.draw;
    }
    
    // Calculate stake using Kelly Criterion
    const trueProbability = calculateTrueProbability(factors);
    const kellyFraction = (odds * trueProbability - 1) / (odds - 1);
    const stake = Math.max(1, Math.min(10, kellyFraction * 100)); // 1-10% of bankroll
    
    // Calculate expected value
    const expectedValue = ((trueProbability * odds) - 1) * 100;
    
    return {
        outcome,
        odds: odds.toFixed(2),
        stake: stake.toFixed(1),
        expectedValue: expectedValue.toFixed(1),
        reasoning: generateReasoning(factors, outcome)
    };
}

// Generate reasoning for prediction
function generateReasoning(factors, outcome) {
    const reasons = [];
    
    if (factors.form > 0.6) reasons.push('Excelente forma recente');
    if (factors.h2h > 0.6) reasons.push('Histórico favorável');
    if (factors.homeAdvantage > 0.55) reasons.push('Forte vantagem de casa');
    if (factors.injuries > 0.6) reasons.push('Adversário com lesões importantes');
    if (factors.motivation > 0.6) reasons.push('Alta motivação');
    if (factors.weather < 0.4) reasons.push('Condições climáticas adversas');
    
    return reasons.slice(0, 3).join(', ') || 'Análise equilibrada dos fatores';
}

// Load Super Odds (Intelligent Multiples)
async function loadSuperOdds() {
    const container = document.getElementById('superOddsContainer');
    container.innerHTML = '<div class="loading">Analisando correlações negativas para Super Odds...</div>';
    
    try {
        // Get multiple matches for correlation analysis
        const matches = await getFootballMatches();
        const superMultiples = await findIntelligentMultiples(matches);
        
        container.innerHTML = '';
        superMultiples.forEach(multiple => {
            container.appendChild(createSuperOddsCard(multiple));
        });
        
    } catch (error) {
        console.error('Error loading super odds:', error);
        container.innerHTML = '<div class="loading">Erro ao carregar Super Odds. Tente novamente.</div>';
    }
}

// Find intelligent multiples with negative correlation
async function findIntelligentMultiples(matches) {
    const multiples = [];
    
    // Analyze matches for correlation opportunities
    for (let i = 0; i < matches.length - 2; i++) {
        for (let j = i + 1; j < matches.length - 1; j++) {
            for (let k = j + 1; k < matches.length; k++) {
                const match1 = matches[i];
                const match2 = matches[j];
                const match3 = matches[k];
                
                // Check for negative correlation
                const correlation = await analyzeCorrelation([match1, match2, match3]);
                
                if (correlation.isNegativelyCorrelated && correlation.totalOdds >= 50) {
                    const analysis = await analyzeMultiple([match1, match2, match3]);
                    
                    if (analysis.confidence >= 35) { // Lower threshold for super odds
                        multiples.push({
                            matches: [match1, match2, match3],
                            correlation,
                            analysis,
                            totalOdds: correlation.totalOdds
                        });
                    }
                }
            }
        }
    }
    
    // Sort by total odds (highest first)
    return multiples.sort((a, b) => b.totalOdds - a.totalOdds).slice(0, 5);
}

// Analyze correlation between matches
async function analyzeCorrelation(matches) {
    let totalOdds = 1;
    let isNegativelyCorrelated = true;
    const correlationFactors = [];
    
    for (const match of matches) {
        const analysis = await performDeepAnalysis(match, BETTING_PROFILES.SUPER_ODDS);
        const odds = parseFloat(analysis.prediction.odds);
        
        totalOdds *= odds;
        correlationFactors.push({
            match: match,
            factors: analysis.factors,
            odds: odds
        });
    }
    
    // Check for negative correlation patterns
    // Different leagues, different time zones, different playing styles
    const leagues = new Set(matches.map(m => m.league.id));
    const countries = new Set(matches.map(m => m.league.country));
    
    if (leagues.size < 2 || countries.size < 2) {
        isNegativelyCorrelated = false;
    }
    
    return {
        isNegativelyCorrelated,
        totalOdds,
        correlationFactors,
        diversification: {
            leagues: leagues.size,
            countries: countries.size
        }
    };
}

// Analyze multiple bet combination
async function analyzeMultiple(matches) {
    let combinedConfidence = 1;
    const predictions = [];
    
    for (const match of matches) {
        const analysis = await performDeepAnalysis(match, BETTING_PROFILES.SUPER_ODDS);
        const individualProbability = analysis.confidence / 100;
        
        combinedConfidence *= individualProbability;
        predictions.push(analysis.prediction);
    }
    
    // Convert to percentage
    const confidence = combinedConfidence * 100;
    
    return {
        confidence,
        predictions,
        expectedValue: calculateMultipleExpectedValue(predictions, combinedConfidence),
        riskLevel: 'MAXIMUM'
    };
}

// Calculate expected value for multiple
function calculateMultipleExpectedValue(predictions, combinedProbability) {
    const totalOdds = predictions.reduce((acc, pred) => acc * parseFloat(pred.odds), 1);
    return ((combinedProbability * totalOdds) - 1) * 100;
}

// Create Super Odds card
function createSuperOddsCard(multiple) {
    const card = document.createElement('div');
    card.className = 'recommendation-card super-odds-card';
    
    const matchesHtml = multiple.matches.map(match => {
        const prediction = multiple.analysis.predictions.find(p => p.match === match);
        return `
            <div class="multiple-match">
                <div class="match-teams">${match.teams.home.name} vs ${match.teams.away.name}</div>
                <div class="match-prediction">${prediction?.outcome || 'Home'} @ ${prediction?.odds || '2.00'}x</div>
            </div>
        `;
    }).join('');
    
    card.innerHTML = `
        <div class="super-odds-header">
            <h3>💎 SUPER ODDS MÚLTIPLA</h3>
            <div class="total-odds">${multiple.totalOdds.toFixed(0)}x</div>
        </div>
        
        <div class="multiple-matches">
            ${matchesHtml}
        </div>
        
        <div class="multiple-stats">
            <div class="stat">
                <span class="stat-label">Odds Total:</span>
                <span class="stat-value">${multiple.totalOdds.toFixed(0)}x</span>
            </div>
            <div class="stat">
                <span class="stat-label">Confiança:</span>
                <span class="stat-value">${multiple.analysis.confidence.toFixed(1)}%</span>
            </div>
            <div class="stat">
                <span class="stat-label">Valor Esperado:</span>
                <span class="stat-value">+${multiple.analysis.expectedValue.toFixed(0)}%</span>
            </div>
            <div class="stat">
                <span class="stat-label">Diversificação:</span>
                <span class="stat-value">${multiple.correlation.diversification.leagues} ligas, ${multiple.correlation.diversification.countries} países</span>
            </div>
        </div>
        
        <div class="risk-warning">
            ⚠️ RISCO MÁXIMO: Aposte apenas o que pode perder. Esta é uma aposta de altíssimo risco com potencial de retorno extremo.
        </div>
    `;
    
    return card;
}

// Additional CSS for Super Odds cards
const superOddsCSS = `
    .super-odds-card {
        border: 2px solid #f59e0b;
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(239, 68, 68, 0.1));
    }
    
    .super-odds-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .super-odds-header h3 {
        margin: 0;
        color: #f59e0b;
        font-size: 1.2rem;
    }
    
    .total-odds {
        font-size: 2rem;
        font-weight: 800;
        color: #f59e0b;
        text-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
    }
    
    .multiple-matches {
        margin-bottom: 1rem;
    }
    
    .multiple-match {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .match-teams {
        color: #ffffff;
        font-weight: 500;
    }
    
    .match-prediction {
        color: #10b981;
        font-weight: 600;
    }
    
    .multiple-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .risk-warning {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid #ef4444;
        border-radius: 8px;
        padding: 1rem;
        color: #fecaca;
        font-size: 0.9rem;
        text-align: center;
        font-weight: 500;
    }
`;

// Inject Super Odds CSS
const style = document.createElement('style');
style.textContent = superOddsCSS;
document.head.appendChild(style);
