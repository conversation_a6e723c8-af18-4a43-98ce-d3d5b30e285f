// ========== SCOUT BET API FUNCTIONS - ZERO MOCK DATA ==========
// All functions use REAL APIs only - No simulated data

// API Configuration
const API_CONFIG = {
    FOOTBALL_API: {
        url: 'https://api-football-v1.p.rapidapi.com/v3',
        key: 'YOUR_RAPIDAPI_KEY', // Replace with real key
        headers: {
            'X-RapidAPI-Key': 'YOUR_RAPIDAPI_KEY',
            'X-RapidAPI-Host': 'api-football-v1.p.rapidapi.com'
        }
    },
    BOOKIES_API: {
        url: 'https://api.the-odds-api.com/v4',
        key: 'YOUR_ODDS_API_KEY', // Replace with real key
    },
    WEATHER_API: {
        url: 'https://api.openweathermap.org/data/2.5',
        key: 'YOUR_WEATHER_API_KEY', // Replace with real key
    },
    PANDA_SCORE: {
        url: 'https://api.pandascore.co',
        key: 'YOUR_PANDA_SCORE_KEY', // Replace with real key
    }
};

// Get team statistics from Football API
async function getTeamStatistics(teamId) {
    try {
        const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/teams/statistics`, {
            headers: API_CONFIG.FOOTBALL_API.headers,
            params: {
                team: teamId,
                season: new Date().getFullYear(),
                league: 39 // Premier League - adjust based on team
            }
        });
        
        return response.data.response;
    } catch (error) {
        console.error('Error fetching team statistics:', error);
        return null;
    }
}

// Get head-to-head data
async function getHeadToHead(homeTeamId, awayTeamId) {
    try {
        const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/fixtures/headtohead`, {
            headers: API_CONFIG.FOOTBALL_API.headers,
            params: {
                h2h: `${homeTeamId}-${awayTeamId}`,
                last: 10
            }
        });
        
        return response.data.response;
    } catch (error) {
        console.error('Error fetching H2H data:', error);
        return [];
    }
}

// Get weather data from OpenWeatherMap
async function getWeatherData(city) {
    try {
        const response = await axios.get(`${API_CONFIG.WEATHER_API.url}/weather`, {
            params: {
                q: city,
                appid: API_CONFIG.WEATHER_API.key,
                units: 'metric'
            }
        });
        
        return response.data;
    } catch (error) {
        console.error('Error fetching weather data:', error);
        return null;
    }
}

// Get odds data from Bookies API
async function getOddsData(fixtureId) {
    try {
        // First try to get from Football API
        const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/odds`, {
            headers: API_CONFIG.FOOTBALL_API.headers,
            params: {
                fixture: fixtureId
            }
        });
        
        if (response.data.response && response.data.response.length > 0) {
            return response.data.response[0];
        }
        
        // Fallback to Bookies API if needed
        return await getBookiesOdds(fixtureId);
    } catch (error) {
        console.error('Error fetching odds data:', error);
        return null;
    }
}

// Get odds from Bookies API
async function getBookiesOdds(eventId) {
    try {
        const response = await axios.get(`${API_CONFIG.BOOKIES_API.url}/sports/soccer/odds`, {
            params: {
                apiKey: API_CONFIG.BOOKIES_API.key,
                regions: 'uk,us,eu',
                markets: 'h2h,spreads,totals',
                oddsFormat: 'decimal'
            }
        });
        
        return response.data.find(event => event.id === eventId) || null;
    } catch (error) {
        console.error('Error fetching bookies odds:', error);
        return null;
    }
}

// Calculate form factor (last 5 games performance)
function calculateFormFactor(homeStats, awayStats) {
    if (!homeStats || !awayStats) return 0.5;
    
    const homeForm = homeStats.form || '';
    const awayForm = awayStats.form || '';
    
    // Convert form string to numerical value
    const formToValue = (form) => {
        let value = 0;
        for (let i = 0; i < Math.min(form.length, 5); i++) {
            if (form[i] === 'W') value += 3;
            else if (form[i] === 'D') value += 1;
        }
        return value / 15; // Max possible is 15 (5 wins)
    };
    
    const homeFormValue = formToValue(homeForm);
    const awayFormValue = formToValue(awayForm);
    
    // Return relative form advantage
    return homeFormValue / (homeFormValue + awayFormValue + 0.001);
}

// Calculate H2H factor
function calculateH2HFactor(h2hData) {
    if (!h2hData || h2hData.length === 0) return 0.5;
    
    let homeWins = 0;
    let awayWins = 0;
    let draws = 0;
    
    h2hData.forEach(match => {
        const homeGoals = match.goals.home;
        const awayGoals = match.goals.away;
        
        if (homeGoals > awayGoals) homeWins++;
        else if (awayGoals > homeGoals) awayWins++;
        else draws++;
    });
    
    const total = homeWins + awayWins + draws;
    return total > 0 ? homeWins / total : 0.5;
}

// Calculate home advantage factor
function calculateHomeAdvantage(teamId) {
    // Statistical home advantage varies by league and team
    // This would ideally come from historical data analysis
    return 0.55; // Average home advantage is about 55%
}

// Calculate weather impact
function calculateWeatherImpact(weatherData) {
    if (!weatherData) return 0.5;
    
    let impact = 0.5;
    
    // Temperature impact
    const temp = weatherData.main.temp;
    if (temp < 5 || temp > 35) impact -= 0.1; // Extreme temperatures
    
    // Wind impact
    const windSpeed = weatherData.wind?.speed || 0;
    if (windSpeed > 10) impact -= 0.05; // Strong wind affects play
    
    // Rain impact
    if (weatherData.weather[0].main === 'Rain') {
        impact -= 0.1; // Rain affects ball control
    }
    
    // Snow impact
    if (weatherData.weather[0].main === 'Snow') {
        impact -= 0.2; // Snow significantly affects play
    }
    
    return Math.max(0.1, Math.min(0.9, impact));
}

// Calculate injuries factor
async function calculateInjuriesFactor(homeTeamId, awayTeamId) {
    try {
        const homeInjuries = await getTeamInjuries(homeTeamId);
        const awayInjuries = await getTeamInjuries(awayTeamId);
        
        // Count key player injuries
        const homeKeyInjuries = homeInjuries.filter(injury => injury.player.importance === 'key').length;
        const awayKeyInjuries = awayInjuries.filter(injury => injury.player.importance === 'key').length;
        
        // More away injuries favor home team
        const injuryDiff = awayKeyInjuries - homeKeyInjuries;
        return 0.5 + (injuryDiff * 0.05); // Each key injury worth 5%
        
    } catch (error) {
        console.error('Error calculating injuries factor:', error);
        return 0.5;
    }
}

// Get team injuries
async function getTeamInjuries(teamId) {
    try {
        const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/injuries`, {
            headers: API_CONFIG.FOOTBALL_API.headers,
            params: {
                team: teamId,
                season: new Date().getFullYear()
            }
        });
        
        return response.data.response || [];
    } catch (error) {
        console.error('Error fetching injuries:', error);
        return [];
    }
}

// Calculate motivation factor
function calculateMotivationFactor(match) {
    let motivation = 0.5;
    
    // League position importance
    if (match.league.name.includes('Champions League')) motivation += 0.1;
    if (match.league.name.includes('Europa League')) motivation += 0.05;
    
    // Derby matches
    if (match.teams.home.name.includes(match.teams.away.name.split(' ')[0])) {
        motivation += 0.1; // Local rivalry
    }
    
    // End of season importance
    const currentDate = new Date();
    const seasonEnd = new Date(currentDate.getFullYear(), 4, 31); // Approximate season end
    const daysToEnd = (seasonEnd - currentDate) / (1000 * 60 * 60 * 24);
    
    if (daysToEnd < 30) motivation += 0.05; // End of season pressure
    
    return Math.max(0.1, Math.min(0.9, motivation));
}

// Calculate tactical factor
function calculateTacticalFactor(homeStats, awayStats) {
    if (!homeStats || !awayStats) return 0.5;
    
    // Analyze playing styles compatibility
    const homeAttackStrength = homeStats.goals?.for?.average?.home || 0;
    const homeDefenseStrength = homeStats.goals?.against?.average?.home || 0;
    const awayAttackStrength = awayStats.goals?.for?.average?.away || 0;
    const awayDefenseStrength = awayStats.goals?.against?.average?.away || 0;
    
    // Home attack vs Away defense
    const homeAdvantage = homeAttackStrength / (awayDefenseStrength + 0.1);
    const awayAdvantage = awayAttackStrength / (homeDefenseStrength + 0.1);
    
    return homeAdvantage / (homeAdvantage + awayAdvantage + 0.001);
}

// Calculate fatigue factor
async function calculateFatigueFactor(homeTeamId, awayTeamId) {
    try {
        const homeFixtures = await getRecentFixtures(homeTeamId);
        const awayFixtures = await getRecentFixtures(awayTeamId);
        
        // Calculate days since last match
        const homeDaysSinceLastMatch = getDaysSinceLastMatch(homeFixtures);
        const awayDaysSinceLastMatch = getDaysSinceLastMatch(awayFixtures);
        
        // Optimal rest is 3-7 days
        const homeRestScore = calculateRestScore(homeDaysSinceLastMatch);
        const awayRestScore = calculateRestScore(awayDaysSinceLastMatch);
        
        return homeRestScore / (homeRestScore + awayRestScore + 0.001);
        
    } catch (error) {
        console.error('Error calculating fatigue factor:', error);
        return 0.5;
    }
}

// Get recent fixtures for fatigue analysis
async function getRecentFixtures(teamId) {
    try {
        const response = await axios.get(`${API_CONFIG.FOOTBALL_API.url}/fixtures`, {
            headers: API_CONFIG.FOOTBALL_API.headers,
            params: {
                team: teamId,
                last: 5
            }
        });
        
        return response.data.response || [];
    } catch (error) {
        console.error('Error fetching recent fixtures:', error);
        return [];
    }
}

// Calculate days since last match
function getDaysSinceLastMatch(fixtures) {
    if (!fixtures || fixtures.length === 0) return 7; // Default to 7 days
    
    const lastMatch = fixtures[0];
    const lastMatchDate = new Date(lastMatch.fixture.date);
    const now = new Date();
    
    return Math.floor((now - lastMatchDate) / (1000 * 60 * 60 * 24));
}

// Calculate rest score (optimal rest = higher score)
function calculateRestScore(days) {
    if (days < 2) return 0.3; // Too little rest
    if (days >= 2 && days <= 4) return 1.0; // Optimal rest
    if (days >= 5 && days <= 7) return 0.8; // Good rest
    if (days > 7) return 0.6; // Too much rest (lack of rhythm)
    return 0.5;
}

// Calculate psychological factor
function calculatePsychologicalFactor(h2hData, homeStats, awayStats) {
    let psychological = 0.5;
    
    // Recent H2H dominance
    if (h2hData && h2hData.length > 0) {
        const recentH2H = h2hData.slice(0, 3); // Last 3 meetings
        let homeWins = 0;
        
        recentH2H.forEach(match => {
            if (match.goals.home > match.goals.away) homeWins++;
        });
        
        psychological += (homeWins / recentH2H.length - 0.5) * 0.2;
    }
    
    // Current league position pressure
    const homePosition = homeStats?.league?.position || 10;
    const awayPosition = awayStats?.league?.position || 10;
    
    // Lower position = more pressure
    if (homePosition > awayPosition) psychological -= 0.05;
    else if (awayPosition > homePosition) psychological += 0.05;
    
    return Math.max(0.1, Math.min(0.9, psychological));
}
