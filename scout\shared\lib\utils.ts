// Utilidades compartilhadas entre todos os projetos ScoutBet

import { 
  Match, 
  Bet, 
  BetSelection,
  OddsData,
  RiskLevel,
  TeamStatistics,
  FormData,
  PredictionData,
  MarketType,
  BetSlip,
  StakeRecommendation
} from '../types';

// ========== FORMATAÇÃO ==========

export function formatOdds(
  odds: number,
  format: 'decimal' | 'fractional' | 'american' = 'decimal'
): string {
  switch (format) {
    case 'decimal':
      return odds.toFixed(2);
    
    case 'fractional':
      if (odds <= 1) return '0/1';
      const fraction = odds - 1;
      const denominator = 1;
      const numerator = Math.round(fraction * 100);
      const gcd = getGCD(numerator, 100);
      return `${numerator / gcd}/${100 / gcd}`;
    
    case 'american':
      if (odds >= 2) {
        return `+${Math.round((odds - 1) * 100)}`;
      } else {
        return `-${Math.round(100 / (odds - 1))}`;
      }
    
    default:
      return odds.toFixed(2);
  }
}

export function formatCurrency(
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount);
}

export function formatDate(
  date: string | Date,
  format: 'short' | 'long' | 'time' | 'full' = 'short',
  locale: string = 'en-US'
): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  
  switch (format) {
    case 'short':
      return d.toLocaleDateString(locale);
    
    case 'long':
      return d.toLocaleDateString(locale, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    
    case 'time':
      return d.toLocaleTimeString(locale, {
        hour: '2-digit',
        minute: '2-digit'
      });
    
    case 'full':
      return d.toLocaleString(locale, {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    
    default:
      return d.toLocaleDateString(locale);
  }
}

export function formatPercentage(value: number, decimals: number = 0): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

export function formatScore(homeScore?: number, awayScore?: number): string {
  if (homeScore === undefined || awayScore === undefined) return '-';
  return `${homeScore} - ${awayScore}`;
}

// ========== CÁLCULOS DE APOSTAS ==========

export function calculateTotalOdds(selections: BetSelection[]): number {
  return selections.reduce((total, selection) => total * selection.odds, 1);
}

export function calculatePotentialWin(stake: number, odds: number): number {
  return stake * odds;
}

export function calculateROI(profit: number, totalStaked: number): number {
  if (totalStaked === 0) return 0;
  return (profit / totalStaked) * 100;
}

export function calculateImpliedProbability(odds: number): number {
  return 1 / odds;
}

export function calculateExpectedValue(
  probability: number,
  odds: number,
  stake: number
): number {
  const winAmount = stake * (odds - 1);
  const loseAmount = stake;
  return (probability * winAmount) - ((1 - probability) * loseAmount);
}

export function calculateKellyCriterion(
  probability: number,
  odds: number,
  bankroll: number,
  kellyFraction: number = 0.25
): StakeRecommendation {
  const b = odds - 1;
  const p = probability;
  const q = 1 - p;
  const kelly = (b * p - q) / b;
  
  const recommendedPercentage = Math.max(0, kelly * kellyFraction);
  const recommendedAmount = bankroll * recommendedPercentage;
  
  return {
    percentage: recommendedPercentage * 100,
    amount: recommendedAmount,
    kellyFactor: kelly
  };
}

export function calculateBookmakerMargin(odds: OddsData): number {
  const { home, draw, away } = odds.averageOdds;
  const impliedProb = 
    calculateImpliedProbability(home) +
    (draw ? calculateImpliedProbability(draw) : 0) +
    calculateImpliedProbability(away);
  
  return (impliedProb - 1) * 100;
}

export function findValueBets(
  predictions: PredictionData,
  odds: OddsData,
  minValue: number = 0.05
): any[] {
  const valueBets = [];
  const { probabilities } = predictions.aiPrediction;
  const { bestOdds } = odds;
  
  // Home
  const homeValue = calculateExpectedValue(
    probabilities.home / 100,
    bestOdds.home.value,
    100
  );
  if (homeValue > minValue * 100) {
    valueBets.push({
      selection: 'home',
      odds: bestOdds.home.value,
      probability: probabilities.home,
      expectedValue: homeValue,
      bookmaker: bestOdds.home.bookmaker
    });
  }
  
  // Draw
  if (probabilities.draw && bestOdds.draw) {
    const drawValue = calculateExpectedValue(
      probabilities.draw / 100,
      bestOdds.draw.value,
      100
    );
    if (drawValue > minValue * 100) {
      valueBets.push({
        selection: 'draw',
        odds: bestOdds.draw.value,
        probability: probabilities.draw,
        expectedValue: drawValue,
        bookmaker: bestOdds.draw.bookmaker
      });
    }
  }
  
  // Away
  const awayValue = calculateExpectedValue(
    probabilities.away / 100,
    bestOdds.away.value,
    100
  );
  if (awayValue > minValue * 100) {
    valueBets.push({
      selection: 'away',
      odds: bestOdds.away.value,
      probability: probabilities.away,
      expectedValue: awayValue,
      bookmaker: bestOdds.away.bookmaker
    });
  }
  
  return valueBets.sort((a, b) => b.expectedValue - a.expectedValue);
}

// ========== ANÁLISE DE RISCO ==========

export function calculateRiskLevel(
  confidence: number,
  odds: number,
  factors: any[]
): RiskLevel {
  // Fatores negativos aumentam o risco
  const negativeFactors = factors.filter(f => f.impact === 'negative').length;
  const totalFactors = factors.length;
  const negativeFactor = negativeFactors / totalFactors;
  
  // Odds muito altas indicam maior risco
  const oddsRisk = odds > 5 ? 0.3 : odds > 3 ? 0.2 : odds > 2 ? 0.1 : 0;
  
  // Confiança baixa aumenta o risco
  const confidenceRisk = confidence < 50 ? 0.3 : confidence < 70 ? 0.2 : 0.1;
  
  const totalRisk = negativeFactor + oddsRisk + confidenceRisk;
  
  if (totalRisk >= 0.6) return 'very_high';
  if (totalRisk >= 0.45) return 'high';
  if (totalRisk >= 0.3) return 'medium';
  if (totalRisk >= 0.15) return 'low';
  return 'very_low';
}

export function assessBetSlipRisk(betSlip: BetSlip): RiskLevel {
  const totalOdds = betSlip.totalOdds;
  const numSelections = betSlip.selections.length;
  
  if (betSlip.type === 'multiple') {
    if (numSelections > 5 || totalOdds > 50) return 'very_high';
    if (numSelections > 3 || totalOdds > 20) return 'high';
    if (numSelections > 2 || totalOdds > 10) return 'medium';
    if (totalOdds > 5) return 'low';
    return 'very_low';
  } else {
    if (totalOdds > 5) return 'high';
    if (totalOdds > 3) return 'medium';
    if (totalOdds > 2) return 'low';
    return 'very_low';
  }
}

// ========== ESTATÍSTICAS ==========

export function calculateTeamForm(results: string[]): number {
  if (results.length === 0) return 0;
  
  const points = results.reduce((total, result) => {
    switch (result) {
      case 'W': return total + 3;
      case 'D': return total + 1;
      case 'L': return total + 0;
      default: return total;
    }
  }, 0);
  
  return (points / (results.length * 3)) * 100;
}

export function calculateAverageGoals(
  goalsFor: number,
  goalsAgainst: number,
  matches: number
): { for: number; against: number; total: number } {
  if (matches === 0) return { for: 0, against: 0, total: 0 };
  
  const avgFor = goalsFor / matches;
  const avgAgainst = goalsAgainst / matches;
  
  return {
    for: avgFor,
    against: avgAgainst,
    total: avgFor + avgAgainst
  };
}

export function calculateStreakInfo(bets: Bet[]): {
  current: { type: 'win' | 'loss'; count: number };
  best: number;
  worst: number;
} {
  if (bets.length === 0) {
    return {
      current: { type: 'win', count: 0 },
      best: 0,
      worst: 0
    };
  }
  
  let currentStreak = 0;
  let currentType: 'win' | 'loss' = 'win';
  let bestStreak = 0;
  let worstStreak = 0;
  let tempStreak = 0;
  let lastType: 'win' | 'loss' | null = null;
  
  bets.forEach(bet => {
    const isWin = bet.status === 'won';
    const type = isWin ? 'win' : 'loss';
    
    if (lastType === null || lastType === type) {
      tempStreak++;
    } else {
      if (lastType === 'win') {
        bestStreak = Math.max(bestStreak, tempStreak);
      } else {
        worstStreak = Math.max(worstStreak, tempStreak);
      }
      tempStreak = 1;
    }
    
    lastType = type;
  });
  
  // Verificar última sequência
  if (lastType === 'win') {
    bestStreak = Math.max(bestStreak, tempStreak);
    currentStreak = tempStreak;
    currentType = 'win';
  } else if (lastType === 'loss') {
    worstStreak = Math.max(worstStreak, tempStreak);
    currentStreak = tempStreak;
    currentType = 'loss';
  }
  
  return {
    current: { type: currentType, count: currentStreak },
    best: bestStreak,
    worst: worstStreak
  };
}

// ========== VALIDAÇÕES ==========

export function validateBetSlip(betSlip: BetSlip): string[] {
  const errors: string[] = [];
  
  if (betSlip.selections.length === 0) {
    errors.push('Bet slip is empty');
  }
  
  if (betSlip.stake <= 0) {
    errors.push('Stake must be greater than 0');
  }
  
  // Verificar conflitos
  const matchIds = new Set<string>();
  betSlip.selections.forEach(selection => {
    if (matchIds.has(selection.match.id)) {
      errors.push(`Multiple selections from the same match: ${selection.match.homeTeam.name} vs ${selection.match.awayTeam.name}`);
    }
    matchIds.add(selection.match.id);
  });
  
  // Verificar odds
  betSlip.selections.forEach(selection => {
    if (selection.odds <= 1) {
      errors.push(`Invalid odds for ${selection.match.homeTeam.name} vs ${selection.match.awayTeam.name}`);
    }
  });
  
  return errors;
}

export function validateEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
}

export function validatePassword(password: string): string[] {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return errors;
}

// ========== HELPERS ==========

export function getGCD(a: number, b: number): number {
  return b ? getGCD(b, a % b) : a;
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

export function groupBy<T>(
  array: T[],
  key: keyof T | ((item: T) => string)
): Record<string, T[]> {
  return array.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : String(item[key]);
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    
    groups[groupKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
}

export function sortMatches(
  matches: Match[],
  sortBy: 'time' | 'league' | 'odds' | 'confidence' = 'time'
): Match[] {
  return [...matches].sort((a, b) => {
    switch (sortBy) {
      case 'time':
        return new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
      
      case 'league':
        return a.league.name.localeCompare(b.league.name);
      
      case 'odds':
        return (b.odds.bestOdds.home.value || 0) - (a.odds.bestOdds.home.value || 0);
      
      case 'confidence':
        return (b.predictions.confidence || 0) - (a.predictions.confidence || 0);
      
      default:
        return 0;
    }
  });
}

export function filterMatches(
  matches: Match[],
  filters: FilterOptions
): Match[] {
  return matches.filter(match => {
    // Filtro por esporte
    if (filters.sports?.length && !filters.sports.includes(match.sport)) {
      return false;
    }
    
    // Filtro por liga
    if (filters.leagues?.length && !filters.leagues.includes(match.league.id)) {
      return false;
    }
    
    // Filtro por status
    if (filters.status?.length && !filters.status.includes(match.status)) {
      return false;
    }
    
    // Filtro por data
    if (filters.dateRange) {
      const matchDate = new Date(match.startTime);
      const start = new Date(filters.dateRange.start);
      const end = new Date(filters.dateRange.end);
      
      if (matchDate < start || matchDate > end) {
        return false;
      }
    }
    
    // Filtro por odds
    if (filters.oddsRange) {
      const avgOdds = (
        match.odds.averageOdds.home +
        (match.odds.averageOdds.draw || 0) +
        match.odds.averageOdds.away
      ) / (match.odds.averageOdds.draw ? 3 : 2);
      
      if (avgOdds < filters.oddsRange.min || avgOdds > filters.oddsRange.max) {
        return false;
      }
    }
    
    // Filtro por risco
    if (filters.riskLevels?.length && !filters.riskLevels.includes(match.predictions.risk)) {
      return false;
    }
    
    // Filtro por confiança
    if (filters.confidence && match.predictions.confidence < filters.confidence) {
      return false;
    }
    
    return true;
  });
}

// ========== COLORS ==========

export function getRiskColor(risk: RiskLevel): string {
  switch (risk) {
    case 'very_low': return '#10b981'; // green-500
    case 'low': return '#84cc16'; // lime-500
    case 'medium': return '#f59e0b'; // amber-500
    case 'high': return '#f97316'; // orange-500
    case 'very_high': return '#ef4444'; // red-500
    default: return '#6b7280'; // gray-500
  }
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'live': return '#ef4444'; // red-500
    case 'scheduled': return '#3b82f6'; // blue-500
    case 'finished': return '#6b7280'; // gray-500
    case 'postponed': return '#f59e0b'; // amber-500
    case 'cancelled': return '#dc2626'; // red-600
    default: return '#6b7280'; // gray-500
  }
}

export function getFormColor(result: 'W' | 'D' | 'L'): string {
  switch (result) {
    case 'W': return '#10b981'; // green-500
    case 'D': return '#f59e0b'; // amber-500
    case 'L': return '#ef4444'; // red-500
    default: return '#6b7280'; // gray-500
  }
}