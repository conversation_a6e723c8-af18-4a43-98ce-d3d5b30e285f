// ========== SUPABASE CONFIGURATION ==========
// Scout Bet - Real Database Integration

// Supabase CDN (for browser usage)
const SUPABASE_CDN = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';

// Supabase Configuration
const SUPABASE_CONFIG = {
    url: 'https://rwxzhgksaozgdinhewbw.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ3eHpoZ2tzYW96Z2Rpbmhld2J3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1Nzk2NDgsImV4cCI6MjA2NjE1NTY0OH0.GluQDT4-t2lxc3pFBtcKRClpewqvM8TekJcvGJqT-yg',
    serviceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ3eHpoZ2tzYW96Z2Rpbmhld2J3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDU3OTY0OCwiZXhwIjoyMDY2MTU1NjQ4fQ.heV5Iq5N8qyGofRojNac3qcQEr71RNBVLaue2PPxkJA'
};

// Global Supabase client
let supabase = null;

// Initialize Supabase
async function initializeSupabase() {
    try {
        // Load Supabase from CDN
        if (typeof window !== 'undefined' && !window.supabase) {
            const script = document.createElement('script');
            script.src = SUPABASE_CDN;
            script.onload = () => {
                supabase = window.supabase.createClient(
                    SUPABASE_CONFIG.url,
                    SUPABASE_CONFIG.anonKey
                );
                console.log('✅ Supabase initialized successfully');
            };
            document.head.appendChild(script);
        } else if (window.supabase) {
            supabase = window.supabase.createClient(
                SUPABASE_CONFIG.url,
                SUPABASE_CONFIG.anonKey
            );
            console.log('✅ Supabase initialized successfully');
        }
    } catch (error) {
        console.error('❌ Error initializing Supabase:', error);
    }
}

// Database Schema Setup (run once)
async function setupDatabase() {
    try {
        console.log('🔧 Setting up database schema...');
        
        // This would typically be done via Supabase Dashboard SQL Editor
        // But we can check if tables exist and guide user
        
        const { data: tables, error } = await supabase
            .from('information_schema.tables')
            .select('table_name')
            .eq('table_schema', 'public');
            
        if (error) {
            console.log('ℹ️ Please run the SQL setup in Supabase Dashboard');
            return false;
        }
        
        const tableNames = tables.map(t => t.table_name);
        const requiredTables = ['users', 'user_profiles', 'bets', 'recommendations'];
        const missingTables = requiredTables.filter(table => !tableNames.includes(table));
        
        if (missingTables.length > 0) {
            console.log('⚠️ Missing tables:', missingTables);
            console.log('📋 Please run the SQL setup script in Supabase Dashboard');
            return false;
        }
        
        console.log('✅ Database schema is ready');
        return true;
        
    } catch (error) {
        console.error('❌ Error checking database schema:', error);
        return false;
    }
}

// SQL Schema for Supabase Dashboard
const DATABASE_SCHEMA = `
-- ========== SCOUT BET DATABASE SCHEMA ==========
-- Run this in Supabase SQL Editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    profile_type VARCHAR NOT NULL DEFAULT 'CONSERVATIVE_LOW',
    bankroll DECIMAL DEFAULT 1000.00,
    max_stake_percentage INTEGER DEFAULT 5,
    risk_tolerance VARCHAR DEFAULT 'low',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_profile_type CHECK (
        profile_type IN (
            'CONSERVATIVE_LOW', 'CONSERVATIVE_MEDIUM', 'MEDIUM_MEDIUM',
            'AGGRESSIVE_MEDIUM', 'EXTREME_HIGH', 'SUPER_ODDS'
        )
    )
);

-- Bets table
CREATE TABLE IF NOT EXISTS public.bets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    match_info JSONB NOT NULL,
    prediction JSONB NOT NULL,
    stake DECIMAL NOT NULL,
    odds DECIMAL NOT NULL,
    potential_win DECIMAL NOT NULL,
    status VARCHAR DEFAULT 'pending',
    result VARCHAR NULL,
    actual_win DECIMAL DEFAULT 0,
    confidence INTEGER NOT NULL,
    factors JSONB NOT NULL,
    bet_type VARCHAR DEFAULT 'single',
    placed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    settled_at TIMESTAMP WITH TIME ZONE NULL,
    
    CONSTRAINT valid_status CHECK (
        status IN ('pending', 'won', 'lost', 'void', 'cancelled')
    ),
    CONSTRAINT valid_bet_type CHECK (
        bet_type IN ('single', 'multiple', 'super_odds')
    )
);

-- Recommendations table
CREATE TABLE IF NOT EXISTS public.recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    match_info JSONB NOT NULL,
    analysis JSONB NOT NULL,
    confidence INTEGER NOT NULL,
    recommended_stake DECIMAL NOT NULL,
    expected_value DECIMAL NOT NULL,
    profile_type VARCHAR NOT NULL,
    is_super_odds BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- User statistics table
CREATE TABLE IF NOT EXISTS public.user_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    total_bets INTEGER DEFAULT 0,
    won_bets INTEGER DEFAULT 0,
    lost_bets INTEGER DEFAULT 0,
    void_bets INTEGER DEFAULT 0,
    total_staked DECIMAL DEFAULT 0,
    total_won DECIMAL DEFAULT 0,
    profit DECIMAL DEFAULT 0,
    roi DECIMAL DEFAULT 0,
    win_rate DECIMAL DEFAULT 0,
    average_odds DECIMAL DEFAULT 0,
    biggest_win DECIMAL DEFAULT 0,
    biggest_loss DECIMAL DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    best_streak INTEGER DEFAULT 0,
    worst_streak INTEGER DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_bets_user_id ON public.bets(user_id);
CREATE INDEX IF NOT EXISTS idx_bets_status ON public.bets(status);
CREATE INDEX IF NOT EXISTS idx_bets_placed_at ON public.bets(placed_at);
CREATE INDEX IF NOT EXISTS idx_recommendations_user_id ON public.recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_expires_at ON public.recommendations(expires_at);

-- Row Level Security Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_statistics ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own data" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own bets" ON public.bets
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own recommendations" ON public.recommendations
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own statistics" ON public.user_statistics
    FOR ALL USING (auth.uid() = user_id);

-- Functions for automatic updates
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User')
    );
    
    INSERT INTO public.user_profiles (user_id, profile_type)
    VALUES (NEW.id, 'CONSERVATIVE_LOW');
    
    INSERT INTO public.user_statistics (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE OR REPLACE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update user statistics
CREATE OR REPLACE FUNCTION public.update_user_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update statistics when bet is settled
    IF NEW.status != OLD.status AND NEW.status IN ('won', 'lost', 'void') THEN
        UPDATE public.user_statistics
        SET
            total_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status IN ('won', 'lost', 'void')
            ),
            won_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'won'
            ),
            lost_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'lost'
            ),
            void_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'void'
            ),
            total_staked = (
                SELECT COALESCE(SUM(stake), 0) FROM public.bets 
                WHERE user_id = NEW.user_id AND status IN ('won', 'lost', 'void')
            ),
            total_won = (
                SELECT COALESCE(SUM(actual_win), 0) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'won'
            ),
            updated_at = NOW()
        WHERE user_id = NEW.user_id;
        
        -- Calculate derived statistics
        UPDATE public.user_statistics
        SET
            profit = total_won - total_staked,
            roi = CASE 
                WHEN total_staked > 0 THEN ((total_won - total_staked) / total_staked) * 100
                ELSE 0
            END,
            win_rate = CASE 
                WHEN total_bets > 0 THEN (won_bets::DECIMAL / total_bets::DECIMAL) * 100
                ELSE 0
            END,
            average_odds = (
                SELECT COALESCE(AVG(odds), 0) FROM public.bets 
                WHERE user_id = NEW.user_id AND status IN ('won', 'lost', 'void')
            )
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for statistics updates
CREATE OR REPLACE TRIGGER on_bet_status_change
    AFTER UPDATE ON public.bets
    FOR EACH ROW EXECUTE FUNCTION public.update_user_statistics();

-- Clean up expired recommendations
CREATE OR REPLACE FUNCTION public.cleanup_expired_recommendations()
RETURNS void AS $$
BEGIN
    DELETE FROM public.recommendations 
    WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule cleanup (run this manually or set up a cron job)
-- SELECT cron.schedule('cleanup-recommendations', '0 * * * *', 'SELECT public.cleanup_expired_recommendations();');

COMMENT ON TABLE public.users IS 'Extended user information';
COMMENT ON TABLE public.user_profiles IS 'User betting profiles and preferences';
COMMENT ON TABLE public.bets IS 'User betting history';
COMMENT ON TABLE public.recommendations IS 'AI-generated betting recommendations';
COMMENT ON TABLE public.user_statistics IS 'User performance statistics';
`;

// Export configuration and functions
window.SUPABASE_CONFIG = SUPABASE_CONFIG;
window.initializeSupabase = initializeSupabase;
window.setupDatabase = setupDatabase;
window.DATABASE_SCHEMA = DATABASE_SCHEMA;

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', initializeSupabase);
