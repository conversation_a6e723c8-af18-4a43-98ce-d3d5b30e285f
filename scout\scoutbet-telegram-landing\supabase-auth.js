// ========== SUPABASE AUTHENTICATION ==========
// Scout Bet - Real Authentication System

// Authentication state
let currentUser = null;
let currentProfile = null;
let isLoggedIn = false;

// Initialize authentication
async function initAuth() {
    try {
        // Wait for Supabase to be initialized
        await waitForSupabase();
        
        // Check current session
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
            console.error('Error getting session:', error);
            return;
        }
        
        if (session) {
            await handleAuthSuccess(session.user);
        }
        
        // Listen for auth changes
        supabase.auth.onAuthStateChange(async (event, session) => {
            console.log('Auth state changed:', event);
            
            if (event === 'SIGNED_IN' && session) {
                await handleAuthSuccess(session.user);
            } else if (event === 'SIGNED_OUT') {
                handleAuthSignOut();
            }
        });
        
    } catch (error) {
        console.error('Error initializing auth:', error);
    }
}

// Wait for Supabase to be available
function waitForSupabase() {
    return new Promise((resolve) => {
        const checkSupabase = () => {
            if (window.supabase && supabase) {
                resolve();
            } else {
                setTimeout(checkSupabase, 100);
            }
        };
        checkSupabase();
    });
}

// Handle successful authentication
async function handleAuthSuccess(user) {
    try {
        currentUser = user;
        isLoggedIn = true;
        
        // Get user profile
        const profile = await getUserProfile(user.id);
        if (profile) {
            currentProfile = profile;
            showDashboard();
        } else {
            // New user - show profile selection
            showProfileSelection();
        }
        
    } catch (error) {
        console.error('Error handling auth success:', error);
    }
}

// Handle sign out
function handleAuthSignOut() {
    currentUser = null;
    currentProfile = null;
    isLoggedIn = false;
    
    // Show landing page
    showLandingPage();
}

// Register new user
async function registerUser(userData) {
    try {
        const { data, error } = await supabase.auth.signUp({
            email: userData.email,
            password: userData.password,
            options: {
                data: {
                    name: userData.name
                }
            }
        });
        
        if (error) {
            throw error;
        }
        
        if (data.user) {
            // User created successfully
            alert('Conta criada com sucesso! Verifique seu email para confirmar.');
            return { success: true, user: data.user };
        }
        
    } catch (error) {
        console.error('Registration error:', error);
        return { success: false, error: error.message };
    }
}

// Login user
async function loginUser(email, password) {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        });
        
        if (error) {
            throw error;
        }
        
        return { success: true, user: data.user };
        
    } catch (error) {
        console.error('Login error:', error);
        return { success: false, error: error.message };
    }
}

// Logout user
async function logoutUser() {
    try {
        const { error } = await supabase.auth.signOut();
        
        if (error) {
            throw error;
        }
        
        return { success: true };
        
    } catch (error) {
        console.error('Logout error:', error);
        return { success: false, error: error.message };
    }
}

// Get user profile
async function getUserProfile(userId) {
    try {
        const { data, error } = await supabase
            .from('user_profiles')
            .select(`
                *,
                users (
                    name,
                    email
                )
            `)
            .eq('user_id', userId)
            .single();
        
        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
            throw error;
        }
        
        return data;
        
    } catch (error) {
        console.error('Error getting user profile:', error);
        return null;
    }
}

// Create user profile
async function createUserProfile(userId, profileData) {
    try {
        const { data, error } = await supabase
            .from('user_profiles')
            .insert([{
                user_id: userId,
                profile_type: profileData.profileType,
                bankroll: profileData.bankroll || 1000,
                max_stake_percentage: profileData.maxStake || 5,
                risk_tolerance: profileData.riskTolerance || 'low'
            }])
            .select()
            .single();
        
        if (error) {
            throw error;
        }
        
        return data;
        
    } catch (error) {
        console.error('Error creating user profile:', error);
        return null;
    }
}

// Update user profile
async function updateUserProfile(userId, updates) {
    try {
        const { data, error } = await supabase
            .from('user_profiles')
            .update(updates)
            .eq('user_id', userId)
            .select()
            .single();
        
        if (error) {
            throw error;
        }
        
        return data;
        
    } catch (error) {
        console.error('Error updating user profile:', error);
        return null;
    }
}

// Get user statistics
async function getUserStatistics(userId) {
    try {
        const { data, error } = await supabase
            .from('user_statistics')
            .select('*')
            .eq('user_id', userId)
            .single();
        
        if (error && error.code !== 'PGRST116') {
            throw error;
        }
        
        return data || {
            total_bets: 0,
            won_bets: 0,
            lost_bets: 0,
            total_staked: 0,
            total_won: 0,
            profit: 0,
            roi: 0,
            win_rate: 0
        };
        
    } catch (error) {
        console.error('Error getting user statistics:', error);
        return null;
    }
}

// Save bet to database
async function saveBet(betData) {
    try {
        const { data, error } = await supabase
            .from('bets')
            .insert([{
                user_id: currentUser.id,
                match_info: betData.matchInfo,
                prediction: betData.prediction,
                stake: betData.stake,
                odds: betData.odds,
                potential_win: betData.potentialWin,
                confidence: betData.confidence,
                factors: betData.factors,
                bet_type: betData.betType || 'single'
            }])
            .select()
            .single();
        
        if (error) {
            throw error;
        }
        
        return data;
        
    } catch (error) {
        console.error('Error saving bet:', error);
        return null;
    }
}

// Get user bets
async function getUserBets(userId, limit = 50) {
    try {
        const { data, error } = await supabase
            .from('bets')
            .select('*')
            .eq('user_id', userId)
            .order('placed_at', { ascending: false })
            .limit(limit);
        
        if (error) {
            throw error;
        }
        
        return data || [];
        
    } catch (error) {
        console.error('Error getting user bets:', error);
        return [];
    }
}

// Save recommendation
async function saveRecommendation(recommendationData) {
    try {
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24); // Expire in 24 hours
        
        const { data, error } = await supabase
            .from('recommendations')
            .insert([{
                user_id: currentUser.id,
                match_info: recommendationData.matchInfo,
                analysis: recommendationData.analysis,
                confidence: recommendationData.confidence,
                recommended_stake: recommendationData.recommendedStake,
                expected_value: recommendationData.expectedValue,
                profile_type: currentProfile.profile_type,
                is_super_odds: recommendationData.isSuperOdds || false,
                expires_at: expiresAt.toISOString()
            }])
            .select()
            .single();
        
        if (error) {
            throw error;
        }
        
        return data;
        
    } catch (error) {
        console.error('Error saving recommendation:', error);
        return null;
    }
}

// Get user recommendations
async function getUserRecommendations(userId) {
    try {
        const { data, error } = await supabase
            .from('recommendations')
            .select('*')
            .eq('user_id', userId)
            .gt('expires_at', new Date().toISOString())
            .order('created_at', { ascending: false });
        
        if (error) {
            throw error;
        }
        
        return data || [];
        
    } catch (error) {
        console.error('Error getting user recommendations:', error);
        return [];
    }
}

// Update bet result
async function updateBetResult(betId, result, actualWin = 0) {
    try {
        const { data, error } = await supabase
            .from('bets')
            .update({
                status: result,
                actual_win: actualWin,
                settled_at: new Date().toISOString()
            })
            .eq('id', betId)
            .select()
            .single();
        
        if (error) {
            throw error;
        }
        
        return data;
        
    } catch (error) {
        console.error('Error updating bet result:', error);
        return null;
    }
}

// Check if user is authenticated
function isAuthenticated() {
    return isLoggedIn && currentUser;
}

// Get current user
function getCurrentUser() {
    return currentUser;
}

// Get current profile
function getCurrentProfile() {
    return currentProfile;
}

// Export functions for global use
window.initAuth = initAuth;
window.registerUser = registerUser;
window.loginUser = loginUser;
window.logoutUser = logoutUser;
window.getUserProfile = getUserProfile;
window.createUserProfile = createUserProfile;
window.updateUserProfile = updateUserProfile;
window.getUserStatistics = getUserStatistics;
window.saveBet = saveBet;
window.getUserBets = getUserBets;
window.saveRecommendation = saveRecommendation;
window.getUserRecommendations = getUserRecommendations;
window.updateBetResult = updateBetResult;
window.isAuthenticated = isAuthenticated;
window.getCurrentUser = getCurrentUser;
window.getCurrentProfile = getCurrentProfile;

// Initialize auth when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initAuth, 1000); // Wait for Supabase to load
});
