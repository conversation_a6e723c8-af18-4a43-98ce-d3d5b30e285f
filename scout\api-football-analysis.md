# API Football - Análise de Fatores Disponíveis

## 🔑 API Key Identificada
**Key**: `live_20fd95b073c7e37cd47acb24b2edfe`
**Provider**: API Football (RapidAPI)
**Base URL**: `https://api-football-v1.p.rapidapi.com/v3/`

## 📊 Fatores Adicionais Disponíveis

### **Dados de Times (15+ novos fatores)**
**Endpoint**: `/teams/statistics`
- Jogos em casa/fora (vitórias, empates, derrotas)
- Gols marcados/sofridos por tempo de jogo
- Média de gols por partida
- Clean sheets (jogos sem sofrer gols)
- Failed to score (jogos sem marcar)
- Cartões amarelos/vermelhos
- Corners médios por partida
- Penalties marcados/perdidos
- Biggest winning/losing streaks
- Forma atual (W-D-L últimos jogos)

### **Dados de Jogadores (20+ novos fatores)**
**Endpoint**: `/players/topscorers`, `/players/topassists`
- Artilheiros do campeonato
- Assistências por time
- Cartões por jogador
- Minutos jogados
- Lesões atuais
- Suspensões
- Idade média do elenco
- Nacionalidade dos jogadores
- Valor de mercado
- Posições dos jogadores

### **Estatísticas de Partida (25+ novos fatores)**
**Endpoint**: `/fixtures/statistics`
- Posse de bola
- Chutes totais/no gol
- Passes completados
- Accuracy de passes
- Fouls cometidos
- Corners
- Impedimentos
- Saves do goleiro
- xG (Expected Goals) - **CRUCIAL**
- xGA (Expected Goals Against)
- Duelos ganhos/perdidos
- Dribles completados
- Interceptações
- Tackles

### **Dados Históricos Avançados (10+ novos fatores)**
**Endpoint**: `/fixtures/head2head`
- H2H últimos 20 jogos
- Resultados em casa/fora específicos
- Gols marcados em cada tempo
- Resultados em diferentes competições
- Histórico de cartões entre times
- Penalties em confrontos diretos
- Bigger victories/defeats
- Goals timing distribution
- Weekend vs weekday performance
- Rest days between matches

### **Dados de Liga/Competição (8+ novos fatores)**
**Endpoint**: `/standings`, `/fixtures/rounds`
- Posição na tabela
- Diferença de pontos
- Saldo de gols
- Sequência atual na liga
- Performance contra top 6
- Performance contra bottom 6
- Jogos em casa/fora restantes
- Pressure factor (posição na tabela)

## 🎯 Total de Fatores Possíveis

### **Combinando todas as APIs:**
- **API Football**: ~80 fatores
- **The Odds API**: 8 fatores
- **Weather APIs**: 6 fatores
- **Dados calculados**: 15 fatores

### **TOTAL: 100+ fatores de predição!**

## 📈 Implementação Prioritária

### **Fase 1: Fatores Críticos (25 fatores)**
```typescript
interface CriticalFactors {
  // Expected Goals (xG) - MAIS IMPORTANTE
  expectedGoals: {
    homeXG: number;
    awayXG: number;
    xgDifference: number;
  };
  
  // Form atual
  currentForm: {
    homeFormLast5: string; // "WWLDW"
    awayFormLast5: string;
    homeGoalsLast5: number;
    awayGoalsLast5: number;
  };
  
  // Posição na tabela
  leaguePosition: {
    homePosition: number;
    awayPosition: number;
    pointsDifference: number;
  };
  
  // Estatísticas de casa/fora
  homeAwayStats: {
    homeWinsAtHome: number;
    awayWinsAway: number;
    homeGoalsAtHome: number;
    awayGoalsAway: number;
  };
}
```

### **Fase 2: Fatores Avançados (50+ fatores)**
```typescript
interface AdvancedFactors {
  // Estatísticas de jogo
  matchStats: {
    averagePossession: number;
    shotsOnTarget: number;
    passAccuracy: number;
    cornersAverage: number;
    cardsAverage: number;
  };
  
  // Jogadores chave
  keyPlayers: {
    topScorerGoals: number;
    topAssistPlayer: number;
    injuredKeyPlayers: number;
    suspendedPlayers: number;
  };
  
  // Análise tática
  tacticalAnalysis: {
    formationUsed: string;
    avgGoalsFirstHalf: number;
    avgGoalsSecondHalf: number;
    comebackAbility: number;
  };
}
```

### **Fase 3: Fatores Únicos (100+ fatores)**
```typescript
interface ComprehensiveFactors {
  // Análise psicológica
  mentalFactors: {
    pressureIndex: number; // baseado na posição
    motivationLevel: number; // baseado nos objetivos
    confidenceIndex: number; // baseado nos resultados
  };
  
  // Análise contextual
  contextualFactors: {
    restDaysBetweenMatches: number;
    travelDistance: number;
    competitionImportance: number;
    rivalryIntensity: number;
  };
  
  // Análise de momento
  momentumFactors: {
    goalTimingTrend: number;
    comeFromBehindRecord: number;
    latestTransfers: number;
    managerExperience: number;
  };
}
```

## 🔧 Endpoints Principais da API Football

### **Configuração da API:**
```typescript
const apiFootballConfig = {
  baseURL: 'https://api-football-v1.p.rapidapi.com/v3/',
  headers: {
    'X-RapidAPI-Key': 'live_20fd95b073c7e37cd47acb24b2edfe',
    'X-RapidAPI-Host': 'api-football-v1.p.rapidapi.com'
  }
};
```

### **Endpoints Essenciais:**
```typescript
// Fixtures e estatísticas
GET /fixtures?date=2024-01-01&league=39&season=2024
GET /fixtures/statistics?fixture=12345
GET /fixtures/head2head?h2h=33-34

// Times e jogadores
GET /teams/statistics?league=39&season=2024&team=33
GET /players/topscorers?league=39&season=2024
GET /injuries?league=39&season=2024

// Odds (se disponível)
GET /odds?fixture=12345&bookmaker=8

// Standings
GET /standings?league=39&season=2024
```

## 🎯 Vantagens da API Football

### **Dados Únicos:**
- ✅ **xG (Expected Goals)** - fator mais importante
- ✅ **Estatísticas detalhadas por partida**
- ✅ **Dados de jogadores e lesões**
- ✅ **Histórico completo H2H**
- ✅ **Posições na tabela em tempo real**

### **Qualidade dos Dados:**
- Atualizações em tempo real
- Cobertura de 100+ ligas
- Dados históricos extensos
- Precisão alta

## 📊 Algoritmo de Predição Aprimorado

Com a API Football, o algoritmo pode ser muito mais sofisticado:

```python
def advanced_prediction_algorithm(match_data):
    factors = {
        'xg_based': 0.25,      # Expected Goals - peso alto
        'form_based': 0.20,    # Forma atual dos times
        'h2h_based': 0.15,     # Head-to-head
        'league_position': 0.12, # Posição na tabela
        'home_advantage': 0.10,  # Vantagem casa
        'player_impact': 0.08,   # Jogadores lesionados/suspensos
        'tactical': 0.05,        # Análise tática
        'weather': 0.03,         # Condições climáticas
        'odds_movement': 0.02    # Movimento das odds
    }
    
    confidence = calculate_confidence(factors)
    return prediction, confidence
```

## 🎉 Conclusão

Com a **API Football**, o ScoutBet pode implementar um sistema de predição com **100+ fatores**, incluindo os mais importantes como **xG**, **forma atual**, **estatísticas detalhadas** e **dados de jogadores**.

Isso coloca o projeto no nível de plataformas profissionais de análise esportiva!