# 🔐 Scout Bet - Configuração do Supabase

## ✅ SUPABASE JÁ CONFIGURADO

Suas credenciais do Supabase já estão integradas no sistema:

- **URL**: `https://rwxzhgksaozgdinhewbw.supabase.co`
- **Anon Key**: Configurada ✅
- **Service Key**: Configurada ✅

## 🛠️ PRÓXIMOS PASSOS OBRIGATÓRIOS

### 1. **Configurar Banco de Dados** (OBRIGATÓRIO)

1. **Acesse o Supabase Dashboard**:
   ```
   https://supabase.com/dashboard/project/rwxzhgksaozgdinhewbw
   ```

2. **Vá para SQL Editor**:
   - Clique em "SQL Editor" no menu lateral
   - Clique em "New Query"

3. **Execute o Script SQL**:
   - Copie todo o conteúdo do arquivo `setup-database.sql`
   - Cole no SQL Editor
   - Clique em "Run" para executar

### 2. **Configurar Autenticação** (OBRIGATÓRIO)

1. **Vá para Authentication**:
   - Clique em "Authentication" no menu lateral
   - Vá para "Settings"

2. **Habilitar Email Authentication**:
   - Certifique-se que "Enable email confirmations" está ativado
   - Configure "Site URL": `http://localhost:3000` (ou sua URL)

3. **Configurar Email Templates** (Opcional):
   - Personalize os emails de confirmação
   - Configure SMTP se necessário

### 3. **Testar o Sistema**

1. **Abra o arquivo `index.html`**
2. **Abra o Console do Navegador** (F12)
3. **Verifique se aparece**: `✅ Supabase initialized successfully`
4. **Teste o cadastro** de um usuário
5. **Verifique no Dashboard** se o usuário foi criado

## 📊 **ESTRUTURA DO BANCO CRIADA**

### Tabelas Principais:

#### 1. **`users`** - Usuários
```sql
- id (UUID) - Referência para auth.users
- email (VARCHAR) - Email do usuário  
- name (VARCHAR) - Nome completo
- created_at, updated_at (TIMESTAMP)
```

#### 2. **`user_profiles`** - Perfis de Apostas
```sql
- id (UUID) - Chave primária
- user_id (UUID) - Referência para users
- profile_type (VARCHAR) - Tipo do perfil
- bankroll (DECIMAL) - Banca do usuário
- max_stake_percentage (INTEGER) - % máximo por aposta
- risk_tolerance (VARCHAR) - Tolerância ao risco
```

#### 3. **`bets`** - Histórico de Apostas
```sql
- id (UUID) - Chave primária
- user_id (UUID) - Referência para users
- match_info (JSONB) - Informações do jogo
- prediction (JSONB) - Predição da IA
- stake (DECIMAL) - Valor apostado
- odds (DECIMAL) - Odds da aposta
- status (VARCHAR) - Status (pending/won/lost/void)
- confidence (INTEGER) - Nível de confiança
- factors (JSONB) - Fatores analisados
```

#### 4. **`recommendations`** - Recomendações da IA
```sql
- id (UUID) - Chave primária
- user_id (UUID) - Referência para users
- match_info (JSONB) - Informações do jogo
- analysis (JSONB) - Análise completa
- confidence (INTEGER) - Nível de confiança
- recommended_stake (DECIMAL) - Stake recomendado
- expected_value (DECIMAL) - Valor esperado
- expires_at (TIMESTAMP) - Data de expiração
```

#### 5. **`user_statistics`** - Estatísticas do Usuário
```sql
- id (UUID) - Chave primária
- user_id (UUID) - Referência para users
- total_bets (INTEGER) - Total de apostas
- won_bets (INTEGER) - Apostas ganhas
- total_staked (DECIMAL) - Total apostado
- total_won (DECIMAL) - Total ganho
- profit (DECIMAL) - Lucro/Prejuízo
- roi (DECIMAL) - Return on Investment
- win_rate (DECIMAL) - Taxa de acerto
```

## 🔒 **SEGURANÇA IMPLEMENTADA**

### Row Level Security (RLS):
- ✅ Usuários só veem seus próprios dados
- ✅ Políticas de segurança configuradas
- ✅ Autenticação JWT integrada

### Triggers Automáticos:
- ✅ Criação automática de perfil para novos usuários
- ✅ Atualização automática de estatísticas
- ✅ Limpeza de recomendações expiradas

## 🧪 **COMO TESTAR**

### 1. **Teste de Cadastro**:
```javascript
// No console do navegador
registerUser({
    name: "Teste User",
    email: "<EMAIL>", 
    password: "123456"
});
```

### 2. **Teste de Login**:
```javascript
// No console do navegador
loginUser("<EMAIL>", "123456");
```

### 3. **Verificar Dados**:
```sql
-- No SQL Editor do Supabase
SELECT * FROM public.users;
SELECT * FROM public.user_profiles;
SELECT * FROM public.user_statistics;
```

## ⚠️ **PROBLEMAS COMUNS**

### **Erro: "relation does not exist"**
- **Causa**: Banco não foi configurado
- **Solução**: Execute o script `setup-database.sql`

### **Erro: "JWT expired"**
- **Causa**: Token expirado
- **Solução**: Faça logout e login novamente

### **Erro: "Row Level Security"**
- **Causa**: Políticas de segurança
- **Solução**: Verifique se o usuário está autenticado

### **Usuário não aparece no dashboard**
- **Causa**: Trigger não executou
- **Solução**: Execute manualmente:
```sql
SELECT public.handle_new_user();
```

## 📈 **MONITORAMENTO**

### Dashboard do Supabase:
1. **Database** - Ver tabelas e dados
2. **Authentication** - Usuários cadastrados  
3. **API** - Logs de requisições
4. **Storage** - Arquivos (se usar)

### Logs no Console:
```javascript
// Verificar inicialização
console.log('Supabase status:', supabase);

// Verificar autenticação
console.log('Current user:', getCurrentUser());

// Verificar perfil
console.log('Current profile:', getCurrentProfile());
```

## 🚀 **PRÓXIMAS MELHORIAS**

### Funcionalidades Avançadas:
- [ ] **Real-time subscriptions** para odds ao vivo
- [ ] **Storage** para imagens de perfil
- [ ] **Edge Functions** para análises complexas
- [ ] **Webhooks** para notificações
- [ ] **Analytics** avançados

### Otimizações:
- [ ] **Índices** adicionais para performance
- [ ] **Views** para consultas complexas
- [ ] **Materialized Views** para relatórios
- [ ] **Particionamento** para grandes volumes

---

## ✅ **CHECKLIST DE CONFIGURAÇÃO**

- [ ] Executar `setup-database.sql` no Supabase
- [ ] Habilitar Email Authentication
- [ ] Configurar Site URL
- [ ] Testar cadastro de usuário
- [ ] Testar login
- [ ] Verificar criação de perfil
- [ ] Verificar estatísticas
- [ ] Testar logout

**Após completar todos os itens, o sistema estará 100% funcional com banco de dados real!**

---

**🎯 Scout Bet - Sistema completo com Supabase integrado!**
