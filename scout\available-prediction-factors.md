# Fatores de Predição Disponíveis - ScoutBet APIs

## 📊 APIs Identificadas

### 1. **The Odds API** - `api.the-odds-api.com/v4/`
- Odds de múltiplas casas de apostas
- Mercados: H2H, spreads, totals
- Regiões: US, UK, EU
- Formato: Odds decimais

### 2. **Open-Meteo API** - `api.open-meteo.com/v1/forecast`
- Condições climáticas para venues
- Temperatura, umidade, vento, precipitação

### 3. **OpenWeatherMap API** - `api.openweathermap.org/data/2.5/weather`
- Dados meteorológicos alternativos
- Consultas por cidade

### 4. **Supabase Database**
- Tabelas: matches, odds, predictions, bets, users
- Dados históricos e estatísticas

## 🎯 Fatores Implementáveis (Total: ~45-50 fatores)

### **Categoria 1: <PERSON><PERSON> de Odds (8 fatores)**
✅ **Disponível via The Odds API:**
1. Odds médias entre casas de apostas
2. Movimentação de odds (últimas 24h)
3. Margem das casas de apostas
4. Consenso do mercado
5. Odds de abertura vs atuais
6. Diferença entre maior e menor odd
7. Número de casas oferecendo odds
8. Velocidade de mudança das odds

### **Categoria 2: Condições Meteorológicas (6 fatores)**
✅ **Disponível via Weather APIs:**
1. Temperatura no horário do jogo
2. Umidade relativa
3. Velocidade do vento
4. Precipitação (chuva/neve)
5. Pressão atmosférica
6. Condições gerais (céu limpo/nublado)

### **Categoria 3: Dados Históricos (12 fatores)**
✅ **Disponível via Supabase/Database:**
1. Confrontos diretos (H2H) últimos 5 jogos
2. Resultados dos últimos 10 jogos (casa)
3. Resultados dos últimos 10 jogos (visitante)
4. Média de gols marcados (casa)
5. Média de gols sofridos (casa)
6. Média de gols marcados (visitante)
7. Média de gols sofridos (visitante)
8. Sequência atual de vitórias/derrotas
9. Performance em casa vs fora
10. Histórico no mesmo estádio
11. Histórico na mesma época do ano
12. Tendência de over/under 2.5 gols

### **Categoria 4: Análise de Mercado (8 fatores)**
✅ **Disponível via Odds APIs:**
1. Probabilidade implícita das odds
2. Valor esperado (expected value)
3. Odds sharp vs public bookmakers
4. Movimento de linha (line movement)
5. Volume de apostas estimado
6. Odds de handicap asiático
7. Odds de ambos marcam
8. Odds de cartões/escanteios

### **Categoria 5: Estatísticas Calculadas (12 fatores)**
✅ **Calculável com dados disponíveis:**
1. Força relativa dos times
2. Índice de performance casa/fora
3. Tendência de resultados recentes
4. Consistência de performance
5. Fator mando de campo
6. Índice de eficiência ofensiva
7. Índice de eficiência defensiva
8. Média móvel de gols
9. Variabilidade de resultados
10. Momentum atual do time
11. Índice de form recent
12. Correlation com condições climáticas

## ❌ Fatores Limitados (Não disponíveis nas APIs atuais)

### **Dados que precisariam de APIs adicionais:**
- Lesões de jogadores específicos
- Suspensões e cartões
- Escalações e formações táticas
- Dados de xG (Expected Goals)
- Estatísticas individuais de jogadores
- Dados de posse de bola
- Estatísticas de chutes/finalizações
- Escanteios e cartões históricos
- Dados de velocidade de sprint
- Análise de vídeo/tática

## 📈 Implementação Realista

### **Fase 1: Fatores Básicos (25 fatores)**
- Dados de odds completos
- Histórico de confrontos
- Condições meteorológicas
- Estatísticas básicas de time

### **Fase 2: Análise Avançada (45-50 fatores)**
- Algoritmos de cálculo complexos
- Análise de tendências
- Índices proprietários
- Correlações entre fatores

### **Fase 3: APIs Adicionais (65+ fatores)**
- Integração com APIs de player data
- Dados táticos avançados
- Análise de vídeo
- Social sentiment

## 🔧 Implementação Técnica

### **Estrutura da Predição:**
```typescript
interface PredictionFactors {
  // Odds Analysis (8 factors)
  oddsAnalysis: {
    averageOdds: number;
    oddsMovement: number;
    bookmakerMargin: number;
    marketConsensus: number;
    openingVsCurrent: number;
    oddsDifference: number;
    numberOfBookmakers: number;
    oddsVelocity: number;
  };
  
  // Weather Impact (6 factors)
  weatherImpact: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    precipitation: number;
    pressure: number;
    generalConditions: string;
  };
  
  // Historical Data (12 factors)
  historicalData: {
    h2hLast5: number;
    homeLast10: number;
    awayLast10: number;
    homeGoalsFor: number;
    homeGoalsAgainst: number;
    awayGoalsFor: number;
    awayGoalsAgainst: number;
    currentStreak: number;
    homeAwayForm: number;
    venueRecord: number;
    seasonalTrend: number;
    overUnderTrend: number;
  };
  
  // Market Analysis (8 factors)
  marketAnalysis: {
    impliedProbability: number;
    expectedValue: number;
    sharpVsPublic: number;
    lineMovement: number;
    bettingVolume: number;
    asianHandicap: number;
    bothTeamsScore: number;
    cornersCards: number;
  };
  
  // Calculated Stats (12 factors)
  calculatedStats: {
    relativeStrength: number;
    homeAdvantage: number;
    recentTrend: number;
    consistency: number;
    homeFieldFactor: number;
    offensiveEfficiency: number;
    defensiveEfficiency: number;
    goalsMovingAverage: number;
    resultVariability: number;
    currentMomentum: number;
    formIndex: number;
    weatherCorrelation: number;
  };
}
```

## 🎯 Conclusão

**Com as APIs disponíveis, podemos implementar aproximadamente 45-50 fatores de predição**, que é uma base sólida para um sistema de análise.

Para chegar aos 65+ fatores mencionados, seria necessário integrar APIs adicionais como:
- **Football-Data.org** para estatísticas avançadas
- **Sportradar** para dados de jogadores
- **Opta Sports** para dados táticos
- **APIs de injury reports**
- **APIs de team news**

O sistema atual já permite criar predições robustas com os fatores disponíveis!