// Tipos compartilhados entre todos os projetos ScoutBet

// ========== ENTIDADES PRINCIPAIS ==========

export interface Match {
  id: string;
  sport: 'football' | 'basketball' | 'tennis' | 'baseball';
  homeTeam: Team;
  awayTeam: Team;
  homeScore?: number;
  awayScore?: number;
  status: 'scheduled' | 'live' | 'finished' | 'postponed' | 'cancelled';
  startTime: string;
  minute?: number; // Para jogos ao vivo
  venue: string;
  referee?: string;
  attendance?: number;
  league: League;
  round?: number;
  season: string;
  weather?: WeatherData;
  odds: OddsData;
  predictions: PredictionData;
  statistics?: MatchStatistics;
  markets: Market[];
  lastUpdated: string;
}

export interface Team {
  id: string;
  name: string;
  shortName: string;
  logo: string;
  country: string;
  founded?: number;
  venue?: string;
  manager?: string;
  form: FormData;
  statistics: TeamStatistics;
  injuries: Player[];
  suspensions: Player[];
  keyPlayers: Player[];
}

export interface Player {
  id: string;
  name: string;
  position: string;
  number?: number;
  status: 'fit' | 'injured' | 'suspended' | 'doubtful';
  returnDate?: string;
  importance: 'key' | 'regular' | 'substitute';
}

export interface League {
  id: string;
  name: string;
  country: string;
  logo: string;
  type: 'league' | 'cup' | 'international';
  season: string;
  startDate: string;
  endDate: string;
  currentRound?: number;
  totalRounds?: number;
  standings?: LeagueStanding[];
}

export interface LeagueStanding {
  position: number;
  teamId: string;
  teamName: string;
  played: number;
  won: number;
  drawn: number;
  lost: number;
  goalsFor: number;
  goalsAgainst: number;
  goalDifference: number;
  points: number;
  form: string; // Ex: "WWDLW"
}

// ========== ODDS E APOSTAS ==========

export interface OddsData {
  bookmakers: BookmakerOdds[];
  bestOdds: {
    home: OddValue;
    draw?: OddValue;
    away: OddValue;
  };
  averageOdds: {
    home: number;
    draw?: number;
    away: number;
  };
  movements: OddsMovement[];
  consensus: {
    home: number;
    draw?: number;
    away: number;
  };
}

export interface BookmakerOdds {
  bookmaker: Bookmaker;
  odds: {
    home: number;
    draw?: number;
    away: number;
  };
  markets: MarketOdds[];
  lastUpdated: string;
}

export interface Bookmaker {
  id: string;
  name: string;
  logo: string;
  url?: string;
  margin: number;
  reliability: number; // 0-100
}

export interface OddValue {
  value: number;
  bookmaker: string;
  movement: 'up' | 'down' | 'stable';
  changePercent?: number;
}

export interface OddsMovement {
  timestamp: string;
  bookmaker: string;
  market: string;
  oldValue: number;
  newValue: number;
  selection: string;
}

export interface Market {
  id: string;
  name: string;
  type: MarketType;
  selections: MarketSelection[];
  suspended: boolean;
  cashOutAvailable: boolean;
}

export interface MarketSelection {
  id: string;
  name: string;
  odds: number;
  status: 'active' | 'suspended' | 'settled';
  result?: 'won' | 'lost' | 'void';
}

export type MarketType = 
  | '1X2' 
  | 'Over/Under' 
  | 'Both Teams to Score' 
  | 'Handicap' 
  | 'Correct Score'
  | 'First Goal'
  | 'Half Time/Full Time'
  | 'Double Chance';

export interface MarketOdds {
  marketType: MarketType;
  selections: {
    name: string;
    odds: number;
  }[];
}

// ========== PREVISÕES E ANÁLISE ==========

export interface PredictionData {
  aiPrediction: AIPrediction;
  expertPrediction?: ExpertPrediction;
  communityPrediction?: CommunityPrediction;
  factors: PredictionFactor[];
  confidence: number;
  risk: RiskLevel;
  recommendedBets: RecommendedBet[];
  insights: string[];
}

export interface AIPrediction {
  algorithm: 'neural_network' | 'random_forest' | 'gradient_boost' | 'ensemble';
  version: string;
  probabilities: {
    home: number;
    draw?: number;
    away: number;
  };
  expectedGoals: {
    home: number;
    away: number;
  };
  mostLikelyScore: string;
  confidence: number;
  factors: AIFactor[];
  lastTraining: string;
}

export interface AIFactor {
  name: string;
  category: FactorCategory;
  value: number;
  weight: number;
  impact: 'positive' | 'negative' | 'neutral';
  description: string;
}

export type FactorCategory = 
  | 'form' 
  | 'h2h' 
  | 'weather' 
  | 'injuries' 
  | 'motivation' 
  | 'tactical' 
  | 'home_advantage'
  | 'fatigue'
  | 'psychological';

export interface ExpertPrediction {
  expertId: string;
  expertName: string;
  prediction: string;
  confidence: number;
  reasoning: string;
}

export interface CommunityPrediction {
  totalVotes: number;
  distribution: {
    home: number;
    draw?: number;
    away: number;
  };
}

export interface PredictionFactor {
  id: string;
  name: string;
  category: FactorCategory;
  value: number; // 0-100
  impact: number; // -100 to +100
  description: string;
  subFactors?: {
    name: string;
    value: number;
  }[];
}

export interface RecommendedBet {
  market: MarketType;
  selection: string;
  odds: number;
  confidence: number;
  stake: StakeRecommendation;
  reasoning: string;
  expectedValue: number;
}

export interface StakeRecommendation {
  percentage: number; // % do bankroll
  amount?: number;
  kellyFactor: number;
}

export type RiskLevel = 'very_low' | 'low' | 'medium' | 'high' | 'very_high';

// ========== ESTATÍSTICAS ==========

export interface MatchStatistics {
  possession: { home: number; away: number };
  shots: { home: number; away: number };
  shotsOnTarget: { home: number; away: number };
  corners: { home: number; away: number };
  fouls: { home: number; away: number };
  yellowCards: { home: number; away: number };
  redCards: { home: number; away: number };
  offsides: { home: number; away: number };
  saves: { home: number; away: number };
  passes: { home: number; away: number };
  passAccuracy: { home: number; away: number };
  attacks: { home: number; away: number };
  dangerousAttacks: { home: number; away: number };
}

export interface TeamStatistics {
  overall: SeasonStats;
  home: SeasonStats;
  away: SeasonStats;
  last5: SeasonStats;
  last10: SeasonStats;
  vsTop6: SeasonStats;
  goalsByMinute: GoalDistribution;
  disciplinary: DisciplinaryStats;
}

export interface SeasonStats {
  played: number;
  wins: number;
  draws: number;
  losses: number;
  goalsFor: number;
  goalsAgainst: number;
  goalsPerGame: number;
  concededPerGame: number;
  cleanSheets: number;
  failedToScore: number;
  points: number;
  pointsPerGame: number;
  xG: number; // Expected Goals
  xGA: number; // Expected Goals Against
}

export interface GoalDistribution {
  '0-15': number;
  '16-30': number;
  '31-45': number;
  '46-60': number;
  '61-75': number;
  '76-90': number;
}

export interface DisciplinaryStats {
  yellowCards: number;
  redCards: number;
  foulsCommitted: number;
  foulsReceived: number;
}

export interface FormData {
  recent: MatchResult[];
  homeForm: MatchResult[];
  awayForm: MatchResult[];
  formString: string; // Ex: "WWDLW"
  goalsScored: number;
  goalsConceded: number;
  pointsPerGame: number;
}

export interface MatchResult {
  matchId: string;
  date: string;
  opponent: string;
  opponentId: string;
  result: 'W' | 'D' | 'L';
  score: string;
  goalsFor: number;
  goalsAgainst: number;
  venue: 'home' | 'away';
}

// ========== HEAD TO HEAD ==========

export interface HeadToHead {
  summary: H2HSummary;
  matches: H2HMatch[];
  stats: H2HStats;
}

export interface H2HSummary {
  totalMatches: number;
  homeWins: number;
  draws: number;
  awayWins: number;
  homeGoals: number;
  awayGoals: number;
}

export interface H2HMatch {
  id: string;
  date: string;
  homeTeam: string;
  awayTeam: string;
  homeScore: number;
  awayScore: number;
  competition: string;
  venue: string;
}

export interface H2HStats {
  averageGoals: number;
  over25: number; // % de jogos com mais de 2.5 gols
  bothTeamsScore: number; // % de jogos com ambos marcando
  homeTeamScoredFirst: number; // %
  comebacks: number; // Número de viradas
}

// ========== CLIMA ==========

export interface WeatherData {
  temperature: number;
  feelsLike: number;
  humidity: number;
  windSpeed: number;
  windDirection: string;
  precipitation: number;
  condition: WeatherCondition;
  visibility: number;
  pressure: number;
  uvIndex: number;
  location: string;
  matchTime: string;
  impact: WeatherImpact;
}

export type WeatherCondition = 
  | 'clear' 
  | 'cloudy' 
  | 'rain' 
  | 'heavy_rain' 
  | 'snow' 
  | 'fog' 
  | 'wind';

export interface WeatherImpact {
  overall: number; // -100 to +100
  factors: {
    ballControl: number;
    playerStamina: number;
    injuryRisk: number;
    goalProbability: number;
  };
  description: string;
}

// ========== GESTÃO DE APOSTAS ==========

export interface Bet {
  id: string;
  userId: string;
  type: 'single' | 'multiple' | 'system';
  status: BetStatus;
  matches: BetSelection[];
  stake: number;
  totalOdds: number;
  potentialWin: number;
  actualWin?: number;
  placedAt: string;
  settledAt?: string;
  cashOutAvailable: boolean;
  cashOutValue?: number;
  bonusUsed?: boolean;
  freebet?: boolean;
}

export type BetStatus = 
  | 'pending' 
  | 'won' 
  | 'lost' 
  | 'void' 
  | 'cashout' 
  | 'partial_cashout';

export interface BetSelection {
  matchId: string;
  match: Match;
  market: MarketType;
  selection: string;
  odds: number;
  status: 'pending' | 'won' | 'lost' | 'void';
  settledAt?: string;
}

export interface BetSlip {
  selections: BetSlipSelection[];
  type: 'single' | 'multiple' | 'system';
  stake: number;
  totalOdds: number;
  potentialWin: number;
  bonusAvailable?: Bonus;
  warnings: string[];
}

export interface BetSlipSelection {
  match: Match;
  market: MarketType;
  selection: string;
  odds: number;
  isLocked: boolean;
  conflictsWith?: string[];
}

export interface Bonus {
  id: string;
  type: 'deposit' | 'freebet' | 'cashback' | 'odds_boost';
  value: number;
  requirements: string[];
  expiresAt: string;
}

// ========== USUÁRIO ==========

export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  fullName?: string;
  dateOfBirth?: string;
  country: string;
  currency: string;
  language: string;
  timezone: string;
  balance: number;
  bonusBalance?: number;
  subscription: Subscription;
  preferences: UserPreferences;
  statistics: UserStatistics;
  limits: BettingLimits;
  verification: VerificationStatus;
  createdAt: string;
  lastLoginAt: string;
}

export interface Subscription {
  plan: 'free' | 'basic' | 'premium' | 'pro';
  status: 'active' | 'cancelled' | 'expired';
  startDate: string;
  endDate?: string;
  features: string[];
  maxBetsPerDay?: number;
  maxAnalysisPerDay?: number;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  currency: string;
  timezone: string;
  notifications: NotificationPreferences;
  display: DisplayPreferences;
  betting: BettingPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  inApp: boolean;
  matchReminders: boolean;
  oddsAlerts: boolean;
  results: boolean;
  promotions: boolean;
  tips: boolean;
}

export interface DisplayPreferences {
  oddsFormat: 'decimal' | 'fractional' | 'american';
  dateFormat: string;
  compactView: boolean;
  animations: boolean;
  showFlags: boolean;
  defaultView: 'grid' | 'list';
}

export interface BettingPreferences {
  defaultStake: number;
  oneClickBet: boolean;
  confirmBets: boolean;
  acceptOddsChanges: 'always' | 'higher' | 'never';
  favoriteMarkets: MarketType[];
  favoriteLeagues: string[];
  riskProfile: RiskLevel;
}

export interface UserStatistics {
  totalBets: number;
  wonBets: number;
  lostBets: number;
  voidBets: number;
  winRate: number;
  totalStaked: number;
  totalWon: number;
  totalLost: number;
  profit: number;
  roi: number; // Return on Investment
  averageOdds: number;
  averageStake: number;
  biggestWin: number;
  biggestLoss: number;
  currentStreak: StreakInfo;
  bestStreak: StreakInfo;
  worstStreak: StreakInfo;
  favoriteTeams: TeamPreference[];
  favoriteMarkets: MarketPreference[];
  profitByLeague: ProfitByCategory[];
  profitByMarket: ProfitByCategory[];
  monthlyStats: MonthlyStats[];
}

export interface StreakInfo {
  type: 'win' | 'loss';
  count: number;
  startDate: string;
  endDate?: string;
}

export interface TeamPreference {
  teamId: string;
  teamName: string;
  betsPlaced: number;
  winRate: number;
  profit: number;
}

export interface MarketPreference {
  market: MarketType;
  betsPlaced: number;
  winRate: number;
  profit: number;
}

export interface ProfitByCategory {
  category: string;
  profit: number;
  betsPlaced: number;
  winRate: number;
}

export interface MonthlyStats {
  month: string;
  betsPlaced: number;
  won: number;
  lost: number;
  profit: number;
  roi: number;
}

export interface BettingLimits {
  dailyDeposit: number;
  weeklyDeposit: number;
  monthlyDeposit: number;
  dailyLoss: number;
  weeklyLoss: number;
  monthlyLoss: number;
  maxBetAmount: number;
  maxPayoutAmount: number;
  coolOffPeriod?: CoolOffPeriod;
  selfExclusion?: SelfExclusion;
}

export interface CoolOffPeriod {
  startDate: string;
  endDate: string;
  reason?: string;
}

export interface SelfExclusion {
  startDate: string;
  duration: number; // dias
  reason: string;
  permanent: boolean;
}

export interface VerificationStatus {
  email: boolean;
  phone: boolean;
  identity: boolean;
  address: boolean;
  paymentMethod: boolean;
  level: 'unverified' | 'basic' | 'full';
}

// ========== FILTROS E CONFIGURAÇÕES ==========

export interface FilterOptions {
  sports?: string[];
  leagues?: string[];
  teams?: string[];
  dateRange?: DateRange;
  timeRange?: TimeRange;
  status?: MatchStatus[];
  markets?: MarketType[];
  oddsRange?: OddsRange;
  riskLevels?: RiskLevel[];
  confidence?: number;
  bookmakers?: string[];
}

export interface DateRange {
  start: string;
  end: string;
}

export interface TimeRange {
  start: string; // HH:mm
  end: string; // HH:mm
}

export interface OddsRange {
  min: number;
  max: number;
}

export type MatchStatus = 'scheduled' | 'live' | 'finished' | 'postponed' | 'cancelled';

export interface SortOptions {
  field: SortField;
  direction: 'asc' | 'desc';
}

export type SortField = 
  | 'startTime' 
  | 'league' 
  | 'homeTeam' 
  | 'awayTeam' 
  | 'odds' 
  | 'confidence' 
  | 'risk';

export interface PaginationOptions {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// ========== API ==========

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: ApiError;
  pagination?: PaginationOptions;
  metadata?: ResponseMetadata;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export interface ResponseMetadata {
  requestId: string;
  timestamp: string;
  version: string;
  cached: boolean;
  processingTime: number;
}

export interface WebSocketMessage {
  id: string;
  type: WSMessageType;
  channel: string;
  data: any;
  timestamp: string;
}

export type WSMessageType = 
  | 'match_update' 
  | 'odds_update' 
  | 'goal' 
  | 'red_card'
  | 'match_ended'
  | 'bet_update'
  | 'notification';

// ========== EXPORTAÇÃO ==========

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv' | 'json';
  dateRange: DateRange;
  includeFields: string[];
  filters?: FilterOptions;
  groupBy?: 'date' | 'league' | 'market' | 'team';
  includeSummary: boolean;
  includeCharts: boolean;
  language: string;
}

// ========== ANALYTICS ==========

export interface AnalyticsData {
  overview: AnalyticsOverview;
  performance: PerformanceMetrics;
  trends: TrendData[];
  insights: Insight[];
  predictions: PredictionAccuracy;
}

export interface AnalyticsOverview {
  period: string;
  totalBets: number;
  totalStaked: number;
  totalWon: number;
  profit: number;
  roi: number;
  winRate: number;
  averageOdds: number;
  bestDay: DayPerformance;
  worstDay: DayPerformance;
}

export interface PerformanceMetrics {
  byLeague: PerformanceByCategory[];
  byMarket: PerformanceByCategory[];
  byRiskLevel: PerformanceByCategory[];
  byOddsRange: PerformanceByCategory[];
  byTimeOfDay: PerformanceByCategory[];
  byDayOfWeek: PerformanceByCategory[];
}

export interface PerformanceByCategory {
  category: string;
  bets: number;
  won: number;
  profit: number;
  roi: number;
  winRate: number;
}

export interface TrendData {
  date: string;
  value: number;
  type: 'profit' | 'bets' | 'roi' | 'winRate';
}

export interface Insight {
  id: string;
  type: 'positive' | 'negative' | 'neutral';
  title: string;
  description: string;
  impact: number;
  actionable: boolean;
  action?: string;
}

export interface PredictionAccuracy {
  overall: number;
  byConfidence: {
    range: string;
    accuracy: number;
    count: number;
  }[];
  byMarket: {
    market: MarketType;
    accuracy: number;
    count: number;
  }[];
  improvement: number; // % vs mês anterior
}

export interface DayPerformance {
  date: string;
  profit: number;
  bets: number;
  winRate: number;
}