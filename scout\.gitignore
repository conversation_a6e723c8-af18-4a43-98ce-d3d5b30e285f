# Dependencies
node_modules/
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Testing
coverage/

# Production
build/
dist/
out/

# Next.js
.next/
*.tsbuildinfo
next-env.d.ts

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment variables
.env
.env.*
!.env.example

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Screenshots and test artifacts
*.png
*.jpg
*.jpeg
screenshots/
test-results/

# Database
*.sqlite
*.sqlite3
*.db

# OS files
Thumbs.db
.DS_Store

# Security
*.pem
*.key
*.crt

# Backup files
*.backup
*.bak

# Python
__pycache__/
*.py[cod]
*$py.class
.Python
venv/
env/

# Misc
.cache/