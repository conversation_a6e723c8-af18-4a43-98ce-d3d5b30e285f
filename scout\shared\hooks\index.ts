// React hooks compartilhados entre todos os projetos ScoutBet

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { 
  Match, 
  FilterOptions, 
  SortOptions, 
  PaginationOptions,
  User,
  Bet,
  BetSlip,
  WebSocketMessage,
  ApiResponse,
  AnalyticsData,
  OddsData,
  PredictionData
} from '../types';
import * as api from '../lib/api';
import { WebSocketClient, cache } from '../lib/api';
import { debounce } from '../lib/utils';

// ========== AUTH HOOKS ==========

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Carregar token do localStorage
    const savedToken = localStorage.getItem('auth_token');
    if (savedToken) {
      setToken(savedToken);
      loadUser(savedToken);
    } else {
      setLoading(false);
    }
  }, []);

  const loadUser = async (authToken: string) => {
    try {
      const response = await api.getProfile(authToken);
      if (response.success) {
        setUser(response.data);
      } else {
        throw new Error(response.error?.message || 'Failed to load user');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.login(email, password);
      if (response.success) {
        const { user, token } = response.data;
        setUser(user);
        setToken(token);
        localStorage.setItem('auth_token', token);
        return true;
      } else {
        throw new Error(response.error?.message || 'Login failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: any) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.register(userData);
      if (response.success) {
        const { user, token } = response.data;
        setUser(user);
        setToken(token);
        localStorage.setItem('auth_token', token);
        return true;
      } else {
        throw new Error(response.error?.message || 'Registration failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('auth_token');
    cache.clear();
  };

  const updateProfile = async (updates: Partial<User>) => {
    if (!token) return false;
    
    try {
      const response = await api.updateProfile(updates, token);
      if (response.success) {
        setUser(response.data);
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    }
  };

  return {
    user,
    token,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    isAuthenticated: !!user
  };
}

// ========== MATCHES HOOKS ==========

export function useMatches(
  initialFilters?: FilterOptions,
  initialSort?: SortOptions
) {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterOptions>(initialFilters || {});
  const [sort, setSort] = useState<SortOptions>(initialSort || { field: 'startTime', direction: 'asc' });
  const [pagination, setPagination] = useState<PaginationOptions>({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  });

  const fetchMatches = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.getMatches(filters, sort, pagination);
      if (response.success) {
        setMatches(response.data);
        if (response.pagination) {
          setPagination(response.pagination);
        }
      } else {
        throw new Error(response.error?.message || 'Failed to fetch matches');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [filters, sort, pagination.page, pagination.pageSize]);

  useEffect(() => {
    fetchMatches();
  }, [fetchMatches]);

  const updateFilters = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset página
  };

  const updateSort = (newSort: SortOptions) => {
    setSort(newSort);
  };

  const changePage = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const changePageSize = (pageSize: number) => {
    setPagination(prev => ({ ...prev, pageSize, page: 1 }));
  };

  return {
    matches,
    loading,
    error,
    filters,
    sort,
    pagination,
    updateFilters,
    updateSort,
    changePage,
    changePageSize,
    refresh: fetchMatches
  };
}

export function useMatch(matchId: string) {
  const [match, setMatch] = useState<Match | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMatch = async () => {
      try {
        // Verificar cache primeiro
        const cached = cache.get(`match_${matchId}`);
        if (cached) {
          setMatch(cached);
          setLoading(false);
          return;
        }

        const response = await api.getMatch(matchId);
        if (response.success) {
          setMatch(response.data);
          cache.set(`match_${matchId}`, response.data);
        } else {
          throw new Error(response.error?.message || 'Failed to fetch match');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchMatch();
  }, [matchId]);

  return { match, loading, error };
}

export function useLiveMatches() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const wsClient = useRef<WebSocketClient | null>(null);

  useEffect(() => {
    const fetchLiveMatches = async () => {
      try {
        const response = await api.getLiveMatches();
        if (response.success) {
          setMatches(response.data);
        } else {
          throw new Error(response.error?.message || 'Failed to fetch live matches');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchLiveMatches();

    // Conectar WebSocket para atualizações ao vivo
    wsClient.current = new WebSocketClient();
    wsClient.current.connect();
    wsClient.current.subscribe('live_matches', (message: WebSocketMessage) => {
      if (message.type === 'match_update') {
        setMatches(prev => {
          const index = prev.findIndex(m => m.id === message.data.matchId);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = { ...updated[index], ...message.data.updates };
            return updated;
          }
          return prev;
        });
      }
    });

    return () => {
      wsClient.current?.disconnect();
    };
  }, []);

  return { matches, loading, error };
}

// ========== ODDS HOOKS ==========

export function useOdds(matchId: string, autoRefresh: boolean = false) {
  const [odds, setOdds] = useState<OddsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const wsClient = useRef<WebSocketClient | null>(null);

  useEffect(() => {
    const fetchOdds = async () => {
      try {
        const response = await api.getOdds(matchId);
        if (response.success) {
          setOdds(response.data);
        } else {
          throw new Error(response.error?.message || 'Failed to fetch odds');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchOdds();

    if (autoRefresh) {
      wsClient.current = new WebSocketClient();
      wsClient.current.connect();
      wsClient.current.subscribe(`odds_${matchId}`, (message: WebSocketMessage) => {
        if (message.type === 'odds_update') {
          setOdds(message.data);
        }
      });
    }

    return () => {
      if (wsClient.current) {
        wsClient.current.unsubscribe(`odds_${matchId}`);
        wsClient.current.disconnect();
      }
    };
  }, [matchId, autoRefresh]);

  return { odds, loading, error };
}

// ========== PREDICTIONS HOOKS ==========

export function usePrediction(matchId: string) {
  const [prediction, setPrediction] = useState<PredictionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrediction = async () => {
      try {
        const cached = cache.get(`prediction_${matchId}`);
        if (cached) {
          setPrediction(cached);
          setLoading(false);
          return;
        }

        const response = await api.getPrediction(matchId);
        if (response.success) {
          setPrediction(response.data);
          cache.set(`prediction_${matchId}`, response.data, 30 * 60 * 1000); // 30 min
        } else {
          throw new Error(response.error?.message || 'Failed to fetch prediction');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchPrediction();
  }, [matchId]);

  return { prediction, loading, error };
}

// ========== BETTING HOOKS ==========

export function useBetSlip() {
  const [betSlip, setBetSlip] = useState<BetSlip>({
    selections: [],
    type: 'single',
    stake: 0,
    totalOdds: 1,
    potentialWin: 0,
    warnings: []
  });

  const addSelection = useCallback((selection: any) => {
    setBetSlip(prev => {
      // Verificar se já existe
      const exists = prev.selections.some(s => 
        s.match.id === selection.match.id && 
        s.market === selection.market
      );
      
      if (exists) return prev;

      const newSelections = [...prev.selections, selection];
      const totalOdds = newSelections.reduce((acc, s) => acc * s.odds, 1);
      const potentialWin = prev.stake * totalOdds;

      return {
        ...prev,
        selections: newSelections,
        totalOdds,
        potentialWin,
        type: newSelections.length > 1 ? 'multiple' : 'single'
      };
    });
  }, []);

  const removeSelection = useCallback((matchId: string, market: string) => {
    setBetSlip(prev => {
      const newSelections = prev.selections.filter(s => 
        !(s.match.id === matchId && s.market === market)
      );
      
      const totalOdds = newSelections.reduce((acc, s) => acc * s.odds, 1);
      const potentialWin = prev.stake * totalOdds;

      return {
        ...prev,
        selections: newSelections,
        totalOdds,
        potentialWin,
        type: newSelections.length > 1 ? 'multiple' : 'single'
      };
    });
  }, []);

  const updateStake = useCallback((stake: number) => {
    setBetSlip(prev => ({
      ...prev,
      stake,
      potentialWin: stake * prev.totalOdds
    }));
  }, []);

  const clearBetSlip = useCallback(() => {
    setBetSlip({
      selections: [],
      type: 'single',
      stake: 0,
      totalOdds: 1,
      potentialWin: 0,
      warnings: []
    });
  }, []);

  return {
    betSlip,
    addSelection,
    removeSelection,
    updateStake,
    clearBetSlip
  };
}

export function useBets(token: string | null) {
  const [bets, setBets] = useState<Bet[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBets = useCallback(async (filters?: FilterOptions) => {
    if (!token) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.getBets(token, filters);
      if (response.success) {
        setBets(response.data);
      } else {
        throw new Error(response.error?.message || 'Failed to fetch bets');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [token]);

  useEffect(() => {
    fetchBets();
  }, [fetchBets]);

  const placeBet = async (betSlip: BetSlip) => {
    if (!token) {
      setError('Not authenticated');
      return null;
    }

    try {
      const response = await api.placeBet(betSlip, token);
      if (response.success) {
        setBets(prev => [response.data, ...prev]);
        return response.data;
      } else {
        throw new Error(response.error?.message || 'Failed to place bet');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    }
  };

  return {
    bets,
    loading,
    error,
    fetchBets,
    placeBet
  };
}

// ========== ANALYTICS HOOKS ==========

export function useAnalytics(token: string | null, dateRange?: { start: string; end: string }) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!token) return;

    const fetchAnalytics = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await api.getAnalytics(token, dateRange);
        if (response.success) {
          setAnalytics(response.data);
        } else {
          throw new Error(response.error?.message || 'Failed to fetch analytics');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [token, dateRange?.start, dateRange?.end]);

  return { analytics, loading, error };
}

// ========== UTILITY HOOKS ==========

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error loading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    
    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}

export function useOnClickOutside(
  ref: React.RefObject<HTMLElement>,
  handler: (event: MouseEvent | TouchEvent) => void
) {
  useEffect(() => {
    const listener = (event: MouseEvent | TouchEvent) => {
      if (!ref.current || ref.current.contains(event.target as Node)) {
        return;
      }
      handler(event);
    };

    document.addEventListener('mousedown', listener);
    document.addEventListener('touchstart', listener);

    return () => {
      document.removeEventListener('mousedown', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [ref, handler]);
}

export function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;

    const id = setInterval(() => savedCallback.current(), delay);
    return () => clearInterval(id);
  }, [delay]);
}