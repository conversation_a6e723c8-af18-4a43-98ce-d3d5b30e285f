<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scout Bet - <PERSON>e do <PERSON></title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid #10b981;
            color: #6ee7b7;
        }
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid #ef4444;
            color: #fca5a5;
        }
        .info {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid #3b82f6;
            color: #93c5fd;
        }
        button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 0.5rem;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Scout Bet Scripts -->
    <script src="supabase-config.js"></script>
    <script src="supabase-auth.js"></script>
    <script src="api-functions.js"></script>
    <script src="advanced-analysis-engine.js"></script>

    <div class="container">
        <h1>🎯 Scout Bet - Teste do Sistema</h1>
        <p>Esta página testa todas as funcionalidades do sistema Scout Bet.</p>

        <div class="test-section">
            <h2>🔧 1. Teste de Inicialização</h2>
            <button onclick="testInitialization()">Testar Inicialização</button>
            <div id="init-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 2. Teste de Configurações</h2>
            <button onclick="testConfigurations()">Testar Configurações</button>
            <div id="config-results"></div>
        </div>

        <div class="test-section">
            <h2>🔐 3. Teste de Autenticação</h2>
            <button onclick="testAuth()">Testar Auth Status</button>
            <button onclick="testRegister()">Testar Cadastro</button>
            <button onclick="testLogin()">Testar Login</button>
            <div id="auth-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 4. Teste de Banco de Dados</h2>
            <button onclick="testDatabase()">Testar Conexão DB</button>
            <button onclick="testTables()">Verificar Tabelas</button>
            <div id="db-results"></div>
        </div>

        <div class="test-section">
            <h2>🎮 5. Teste de Perfis</h2>
            <button onclick="testProfiles()">Testar Perfis</button>
            <div id="profiles-results"></div>
        </div>

        <div class="test-section">
            <h2>🧠 6. Teste de Análise Avançada (500+ Fatores)</h2>
            <button onclick="testAdvancedAnalysis()">Testar Análise Avançada</button>
            <button onclick="testFactorCategories()">Testar Categorias de Fatores</button>
            <div id="advanced-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 6. Log do Sistema</h2>
            <button onclick="clearLog()">Limpar Log</button>
            <div id="system-log"></div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('system-log');

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(div);
            console.log(message);
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        // 1. Teste de Inicialização
        async function testInitialization() {
            const container = document.getElementById('init-results');
            container.innerHTML = '';

            try {
                // Verificar se Supabase está carregado
                if (typeof window.supabase !== 'undefined') {
                    container.innerHTML += '<div class="test-result success">✅ Supabase CDN carregado</div>';
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Supabase CDN não carregado</div>';
                }

                // Verificar se cliente Supabase está inicializado
                if (window.supabase && supabase) {
                    container.innerHTML += '<div class="test-result success">✅ Cliente Supabase inicializado</div>';
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Cliente Supabase não inicializado</div>';
                }

                // Verificar funções de auth
                if (typeof window.initAuth === 'function') {
                    container.innerHTML += '<div class="test-result success">✅ Funções de auth carregadas</div>';
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Funções de auth não carregadas</div>';
                }

                log('Teste de inicialização concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro na inicialização: ${error.message}`, 'error');
            }
        }

        // 2. Teste de Configurações
        async function testConfigurations() {
            const container = document.getElementById('config-results');
            container.innerHTML = '';

            try {
                // Aguardar carregamento das configurações
                await new Promise(resolve => setTimeout(resolve, 2000));

                const systemConfig = window.getSystemConfig ? window.getSystemConfig() : null;
                
                if (systemConfig) {
                    container.innerHTML += '<div class="test-result success">✅ Configurações do sistema carregadas</div>';
                    
                    // Testar cada configuração
                    const profiles = window.getSystemConfig('betting_profiles');
                    if (profiles) {
                        container.innerHTML += `<div class="test-result success">✅ Perfis de apostas: ${Object.keys(profiles).length} perfis</div>`;
                    }

                    const weights = window.getAnalysisWeights ? window.getAnalysisWeights() : null;
                    if (weights) {
                        container.innerHTML += '<div class="test-result success">✅ Pesos de análise carregados</div>';
                    }

                    const limits = window.getApiLimits ? window.getApiLimits() : null;
                    if (limits) {
                        container.innerHTML += '<div class="test-result success">✅ Limites de API carregados</div>';
                    }

                    // Mostrar configurações
                    container.innerHTML += `<div class="test-result info"><pre>${JSON.stringify(systemConfig, null, 2)}</pre></div>`;

                } else {
                    container.innerHTML += '<div class="test-result error">❌ Configurações não carregadas</div>';
                }

                log('Teste de configurações concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro nas configurações: ${error.message}`, 'error');
            }
        }

        // 3. Teste de Autenticação
        async function testAuth() {
            const container = document.getElementById('auth-results');
            container.innerHTML = '';

            try {
                const isAuth = window.isAuthenticated ? window.isAuthenticated() : false;
                const currentUser = window.getCurrentUser ? window.getCurrentUser() : null;
                const currentProfile = window.getCurrentProfile ? window.getCurrentProfile() : null;

                container.innerHTML += `<div class="test-result ${isAuth ? 'success' : 'info'}">
                    ${isAuth ? '✅' : 'ℹ️'} Autenticado: ${isAuth}
                </div>`;

                if (currentUser) {
                    container.innerHTML += `<div class="test-result success">✅ Usuário: ${currentUser.email}</div>`;
                }

                if (currentProfile) {
                    container.innerHTML += `<div class="test-result success">✅ Perfil: ${currentProfile.profile_type}</div>`;
                }

                log('Teste de autenticação concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro na autenticação: ${error.message}`, 'error');
            }
        }

        // 4. Teste de Banco de Dados
        async function testDatabase() {
            const container = document.getElementById('db-results');
            container.innerHTML = '';

            try {
                // Testar conexão básica
                const { data, error } = await supabase.from('system_config').select('key').limit(1);
                
                if (error) {
                    container.innerHTML += `<div class="test-result error">❌ Erro de conexão: ${error.message}</div>`;
                } else {
                    container.innerHTML += '<div class="test-result success">✅ Conexão com banco estabelecida</div>';
                }

                log('Teste de banco de dados concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro no banco: ${error.message}`, 'error');
            }
        }

        // 5. Teste de Perfis
        async function testProfiles() {
            const container = document.getElementById('profiles-results');
            container.innerHTML = '';

            try {
                const profiles = window.getSystemConfig ? window.getSystemConfig('betting_profiles') : null;

                if (profiles) {
                    container.innerHTML += `<div class="test-result success">✅ ${Object.keys(profiles).length} perfis encontrados</div>`;

                    Object.entries(profiles).forEach(([key, profile]) => {
                        container.innerHTML += `<div class="test-result info">
                            📊 ${key}: ${profile.name} (Confiança: ${profile.min_confidence}%, Stake: ${profile.max_stake}%)
                        </div>`;
                    });
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Perfis não encontrados</div>';
                }

                log('Teste de perfis concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro nos perfis: ${error.message}`, 'error');
            }
        }

        // 6. Teste de Análise Avançada
        async function testAdvancedAnalysis() {
            const container = document.getElementById('advanced-results');
            container.innerHTML = '';

            try {
                // Verificar se o sistema de análise avançada está carregado
                if (typeof window.performAdvancedAnalysis === 'function') {
                    container.innerHTML += '<div class="test-result success">✅ Sistema de análise avançada carregado</div>';
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Sistema de análise avançada não carregado</div>';
                    return;
                }

                // Verificar fatores de análise
                if (window.SPORT_ANALYSIS_FACTORS) {
                    const footballFactors = window.SPORT_ANALYSIS_FACTORS.FOOTBALL;
                    if (footballFactors) {
                        let totalFactors = 0;
                        Object.values(footballFactors).forEach(category => {
                            Object.values(category).forEach(subcategory => {
                                totalFactors += Object.keys(subcategory).length;
                            });
                        });

                        container.innerHTML += `<div class="test-result success">✅ ${totalFactors} fatores de futebol configurados</div>`;

                        // Mostrar categorias
                        Object.entries(footballFactors).forEach(([categoryName, category]) => {
                            let categoryFactors = 0;
                            Object.values(category).forEach(subcategory => {
                                categoryFactors += Object.keys(subcategory).length;
                            });

                            container.innerHTML += `<div class="test-result info">
                                📊 ${categoryName}: ${categoryFactors} fatores
                            </div>`;
                        });
                    }
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Fatores de análise não encontrados</div>';
                }

                // Teste com jogo simulado
                const mockMatch = {
                    fixture: { id: 'test_123', venue: { city: 'São Paulo' } },
                    teams: {
                        home: { id: 'home_team', name: 'Time Casa' },
                        away: { id: 'away_team', name: 'Time Visitante' }
                    },
                    league: { type: 'League' }
                };

                const mockProfile = { profile_type: 'MEDIUM_MEDIUM' };

                container.innerHTML += '<div class="test-result info">🔄 Executando análise avançada de teste...</div>';

                const analysis = await window.performAdvancedAnalysis(mockMatch, mockProfile);

                if (analysis) {
                    container.innerHTML += `<div class="test-result success">✅ Análise concluída com sucesso</div>`;
                    container.innerHTML += `<div class="test-result info">
                        📊 Fatores analisados: ${analysis.factors_analyzed}<br>
                        🎯 Confiança: ${analysis.confidence_score.toFixed(1)}%<br>
                        💡 Recomendações: ${analysis.recommendations.length}<br>
                        ⚠️ Avaliação de risco: ${analysis.risk_assessment ? 'Ativa' : 'Inativa'}
                    </div>`;

                    // Mostrar detalhes das recomendações
                    if (analysis.recommendations.length > 0) {
                        container.innerHTML += '<div class="test-result info"><strong>Recomendações:</strong><br>';
                        analysis.recommendations.forEach(rec => {
                            container.innerHTML += `• ${rec.type}: ${rec.message}<br>`;
                        });
                        container.innerHTML += '</div>';
                    }
                } else {
                    container.innerHTML += '<div class="test-result error">❌ Falha na análise avançada</div>';
                }

                log('Teste de análise avançada concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro na análise avançada: ${error.message}`, 'error');
            }
        }

        // Teste de categorias de fatores
        async function testFactorCategories() {
            const container = document.getElementById('advanced-results');

            try {
                if (!window.SPORT_ANALYSIS_FACTORS) {
                    container.innerHTML += '<div class="test-result error">❌ Fatores não carregados</div>';
                    return;
                }

                const footballFactors = window.SPORT_ANALYSIS_FACTORS.FOOTBALL;

                container.innerHTML += '<div class="test-result info"><strong>📊 Detalhamento dos Fatores:</strong></div>';

                Object.entries(footballFactors).forEach(([categoryName, category]) => {
                    container.innerHTML += `<div class="test-result info"><strong>${categoryName}:</strong></div>`;

                    Object.entries(category).forEach(([subcategoryName, subcategory]) => {
                        const factorCount = Object.keys(subcategory).length;
                        const avgWeight = Object.values(subcategory).reduce((sum, factor) => sum + Math.abs(factor.weight), 0) / factorCount;

                        container.innerHTML += `<div class="test-result info" style="margin-left: 20px;">
                            • ${subcategoryName}: ${factorCount} fatores (peso médio: ${avgWeight.toFixed(3)})
                        </div>`;
                    });
                });

                log('Teste de categorias de fatores concluído', 'success');

            } catch (error) {
                container.innerHTML += `<div class="test-result error">❌ Erro: ${error.message}</div>`;
                log(`Erro nas categorias: ${error.message}`, 'error');
            }
        }

        // Inicializar testes automaticamente
        window.addEventListener('load', () => {
            log('🎯 Scout Bet - Sistema de Testes Iniciado', 'success');
            
            setTimeout(() => {
                testInitialization();
            }, 1000);

            setTimeout(() => {
                testConfigurations();
            }, 3000);
        });
    </script>
</body>
</html>
