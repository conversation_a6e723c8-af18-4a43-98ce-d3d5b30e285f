-- ========== SCOUT BET DATABASE SETUP ==========
-- Execute este SQL no Supabase SQL Editor
-- URL: https://supabase.com/dashboard/project/rwxzhgksaozgdinhewbw/sql

-- 1. Habilitar Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- 2. <PERSON><PERSON><PERSON> tabel<PERSON> de usu<PERSON> (estende auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR NOT NULL,
    name VARCHAR NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. <PERSON>riar tabela de perfis de usuário
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    profile_type VARCHAR NOT NULL DEFAULT 'CONSERVATIVE_LOW',
    bankroll DECIMAL DEFAULT 1000.00,
    max_stake_percentage INTEGER DEFAULT 5,
    risk_tolerance VARCHAR DEFAULT 'low',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_profile_type CHECK (
        profile_type IN (
            'CONSERVATIVE_LOW', 'CONSERVATIVE_MEDIUM', 'MEDIUM_MEDIUM',
            'AGGRESSIVE_MEDIUM', 'EXTREME_HIGH', 'SUPER_ODDS'
        )
    )
);

-- 4. Criar tabela de apostas
CREATE TABLE IF NOT EXISTS public.bets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    match_info JSONB NOT NULL,
    prediction JSONB NOT NULL,
    stake DECIMAL NOT NULL,
    odds DECIMAL NOT NULL,
    potential_win DECIMAL NOT NULL,
    status VARCHAR DEFAULT 'pending',
    result VARCHAR NULL,
    actual_win DECIMAL DEFAULT 0,
    confidence INTEGER NOT NULL,
    factors JSONB NOT NULL,
    bet_type VARCHAR DEFAULT 'single',
    placed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    settled_at TIMESTAMP WITH TIME ZONE NULL,
    
    CONSTRAINT valid_status CHECK (
        status IN ('pending', 'won', 'lost', 'void', 'cancelled')
    ),
    CONSTRAINT valid_bet_type CHECK (
        bet_type IN ('single', 'multiple', 'super_odds')
    )
);

-- 5. Criar tabela de recomendações
CREATE TABLE IF NOT EXISTS public.recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    match_info JSONB NOT NULL,
    analysis JSONB NOT NULL,
    confidence INTEGER NOT NULL,
    recommended_stake DECIMAL NOT NULL,
    expected_value DECIMAL NOT NULL,
    profile_type VARCHAR NOT NULL,
    is_super_odds BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- 6. Criar tabela de estatísticas do usuário
CREATE TABLE IF NOT EXISTS public.user_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    total_bets INTEGER DEFAULT 0,
    won_bets INTEGER DEFAULT 0,
    lost_bets INTEGER DEFAULT 0,
    void_bets INTEGER DEFAULT 0,
    total_staked DECIMAL DEFAULT 0,
    total_won DECIMAL DEFAULT 0,
    profit DECIMAL DEFAULT 0,
    roi DECIMAL DEFAULT 0,
    win_rate DECIMAL DEFAULT 0,
    average_odds DECIMAL DEFAULT 0,
    biggest_win DECIMAL DEFAULT 0,
    biggest_loss DECIMAL DEFAULT 0,
    current_streak INTEGER DEFAULT 0,
    best_streak INTEGER DEFAULT 0,
    worst_streak INTEGER DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_bets_user_id ON public.bets(user_id);
CREATE INDEX IF NOT EXISTS idx_bets_status ON public.bets(status);
CREATE INDEX IF NOT EXISTS idx_bets_placed_at ON public.bets(placed_at);
CREATE INDEX IF NOT EXISTS idx_recommendations_user_id ON public.recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendations_expires_at ON public.recommendations(expires_at);

-- 8. Habilitar Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_statistics ENABLE ROW LEVEL SECURITY;

-- 9. Criar políticas de segurança
CREATE POLICY "Users can view own data" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own data" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own bets" ON public.bets
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own recommendations" ON public.recommendations
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own statistics" ON public.user_statistics
    FOR ALL USING (auth.uid() = user_id);

-- 10. Função para criar usuário automaticamente
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User')
    );
    
    INSERT INTO public.user_statistics (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Trigger para novos usuários
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 12. Função para atualizar estatísticas
CREATE OR REPLACE FUNCTION public.update_user_statistics()
RETURNS TRIGGER AS $$
BEGIN
    -- Atualizar estatísticas quando aposta é finalizada
    IF NEW.status != OLD.status AND NEW.status IN ('won', 'lost', 'void') THEN
        UPDATE public.user_statistics
        SET
            total_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status IN ('won', 'lost', 'void')
            ),
            won_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'won'
            ),
            lost_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'lost'
            ),
            void_bets = (
                SELECT COUNT(*) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'void'
            ),
            total_staked = (
                SELECT COALESCE(SUM(stake), 0) FROM public.bets 
                WHERE user_id = NEW.user_id AND status IN ('won', 'lost', 'void')
            ),
            total_won = (
                SELECT COALESCE(SUM(actual_win), 0) FROM public.bets 
                WHERE user_id = NEW.user_id AND status = 'won'
            ),
            updated_at = NOW()
        WHERE user_id = NEW.user_id;
        
        -- Calcular estatísticas derivadas
        UPDATE public.user_statistics
        SET
            profit = total_won - total_staked,
            roi = CASE 
                WHEN total_staked > 0 THEN ((total_won - total_staked) / total_staked) * 100
                ELSE 0
            END,
            win_rate = CASE 
                WHEN total_bets > 0 THEN (won_bets::DECIMAL / total_bets::DECIMAL) * 100
                ELSE 0
            END,
            average_odds = (
                SELECT COALESCE(AVG(odds), 0) FROM public.bets 
                WHERE user_id = NEW.user_id AND status IN ('won', 'lost', 'void')
            )
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Trigger para atualização de estatísticas
DROP TRIGGER IF EXISTS on_bet_status_change ON public.bets;
CREATE TRIGGER on_bet_status_change
    AFTER UPDATE ON public.bets
    FOR EACH ROW EXECUTE FUNCTION public.update_user_statistics();

-- 14. Função para limpar recomendações expiradas
CREATE OR REPLACE FUNCTION public.cleanup_expired_recommendations()
RETURNS void AS $$
BEGIN
    DELETE FROM public.recommendations 
    WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 15. Comentários nas tabelas
COMMENT ON TABLE public.users IS 'Informações estendidas dos usuários';
COMMENT ON TABLE public.user_profiles IS 'Perfis de apostas e preferências dos usuários';
COMMENT ON TABLE public.bets IS 'Histórico de apostas dos usuários';
COMMENT ON TABLE public.recommendations IS 'Recomendações de apostas geradas pela IA';
COMMENT ON TABLE public.user_statistics IS 'Estatísticas de performance dos usuários';

-- 16. Inserir dados de exemplo (opcional)
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES ('00000000-0000-0000-0000-000000000001', '<EMAIL>', crypt('demo123', gen_salt('bf')), NOW(), NOW(), NOW());

-- Verificar se tudo foi criado corretamente
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('users', 'user_profiles', 'bets', 'recommendations', 'user_statistics')
ORDER BY tablename;
