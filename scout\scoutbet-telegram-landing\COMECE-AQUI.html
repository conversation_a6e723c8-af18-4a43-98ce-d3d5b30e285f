<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Scout Bet - COMECE AQUI</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            text-align: center;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }
        .big-button {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            text-decoration: none;
            padding: 1.5rem 3rem;
            border-radius: 12px;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }
        .big-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
            background: linear-gradient(135deg, #059669, #047857);
        }
        .secondary-button {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }
        .secondary-button:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 3rem 0;
        }
        .feature {
            background: rgba(255, 255, 255, 0.03);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .feature-desc {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.4;
        }
        .warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
            margin: 2rem 0;
            color: #fbbf24;
        }
        .success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid #10b981;
            border-radius: 8px;
            padding: 1rem;
            margin: 2rem 0;
            color: #6ee7b7;
        }
        .steps {
            text-align: left;
            background: rgba(255, 255, 255, 0.03);
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 1rem 0;
            font-size: 1.1rem;
        }
        .step-number {
            background: #3b82f6;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .footer {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
        }
        @media (max-width: 768px) {
            .container {
                padding: 2rem 1rem;
            }
            .title {
                font-size: 2rem;
            }
            .big-button {
                padding: 1rem 2rem;
                font-size: 1.1rem;
                margin: 0.5rem;
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎯</div>
        <h1 class="title">Scout Bet</h1>
        <p class="subtitle">
            O sistema de apostas esportivas mais avançado do mundo<br>
            <strong>500+ fatores de análise • Zero dados fake • IA avançada</strong>
        </p>

        <div class="success">
            <strong>🎉 SISTEMA 100% PRONTO!</strong><br>
            Todos os arquivos foram criados e o sistema está funcional. Você só precisa configurar o banco de dados.
        </div>

        <!-- Botões Principais -->
        <div style="margin: 2rem 0;">
            <a href="configurador-automatico.html" class="big-button">
                🚀 CONFIGURAR SISTEMA (5 MIN)
            </a>
            <br>
            <a href="index.html" class="big-button secondary-button">
                🎯 USAR SCOUT BET AGORA
            </a>
        </div>

        <div class="warning">
            <strong>⚠️ IMPORTANTE:</strong> Se for a primeira vez, clique em "CONFIGURAR SISTEMA" primeiro. Se já configurou, pode ir direto para "USAR SCOUT BET".
        </div>

        <!-- Funcionalidades -->
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🧠</div>
                <div class="feature-title">500+ Fatores de Análise</div>
                <div class="feature-desc">Sistema baseado em pesquisa científica com análise técnica, física, psicológica e ambiental</div>
            </div>
            <div class="feature">
                <div class="feature-icon">💎</div>
                <div class="feature-title">Super Odds até 200x</div>
                <div class="feature-desc">Múltiplas inteligentes com correlação negativa e diversificação automática</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">6 Perfis Personalizados</div>
                <div class="feature-desc">Do conservador ao extremo, cada perfil com estratégia específica</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">Banco de Dados Real</div>
                <div class="feature-desc">Supabase integrado com suas informações seguras e criptografadas</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🌐</div>
                <div class="feature-title">APIs Reais</div>
                <div class="feature-desc">Dados oficiais de Football API, The Odds API e OpenWeatherMap</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Dashboard Completo</div>
                <div class="feature-desc">Interface intuitiva com estatísticas, análises e recomendações personalizadas</div>
            </div>
        </div>

        <!-- Passos Simples -->
        <div class="steps">
            <h3 style="text-align: center; margin-bottom: 1.5rem;">📋 Como Usar (Super Fácil)</h3>
            
            <div class="step">
                <div class="step-number">1</div>
                <div>Clique em "CONFIGURAR SISTEMA" e siga o passo a passo automático</div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div>Configure o banco de dados (só copiar e colar um código)</div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div>Teste o sistema para ver se tudo funciona</div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div>Use o Scout Bet e comece a fazer análises avançadas!</div>
            </div>
        </div>

        <!-- Botões de Acesso Rápido -->
        <div style="margin: 2rem 0;">
            <h3>🔗 Acesso Rápido</h3>
            <a href="test-system.html" class="big-button secondary-button" style="font-size: 1rem; padding: 1rem 2rem;">
                🧪 Testar Sistema
            </a>
            <a href="COMO-USAR-FACIL.md" class="big-button secondary-button" style="font-size: 1rem; padding: 1rem 2rem;">
                📖 Guia Completo
            </a>
        </div>

        <!-- Suporte -->
        <div class="warning">
            <strong>🆘 PRECISA DE AJUDA?</strong><br>
            Se tiver qualquer dúvida ou problema, é só me chamar aqui no chat que eu te ajudo na hora!
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>🎯 Scout Bet</strong> - Sistema de Análise Preditiva Avançada</p>
            <p>Desenvolvido com tecnologia de ponta • 500+ fatores científicos • Zero dados mock</p>
        </div>
    </div>

    <script>
        // Adicionar efeitos visuais
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 Scout Bet - Sistema carregado!');
            
            // Efeito de hover nos botões
            const buttons = document.querySelectorAll('.big-button');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // Animação das features
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
