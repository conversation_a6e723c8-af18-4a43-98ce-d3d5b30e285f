// API functions compartilhadas entre todos os projetos ScoutBet

import { 
  Match, 
  ApiResponse, 
  FilterOptions, 
  SortOptions, 
  PaginationOptions,
  OddsData,
  PredictionData,
  Bet,
  BetSlip,
  User,
  AnalyticsData,
  ExportOptions,
  WebSocketMessage
} from '../types';

// Configuração base da API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.scoutbet.com/v1';
const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'wss://ws.scoutbet.com';

// Headers padrão
const getHeaders = (token?: string): HeadersInit => ({
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  ...(token && { 'Authorization': `Bearer ${token}` })
});

// ========== MATCHES ==========

export async function getMatches(
  filters?: FilterOptions,
  sort?: SortOptions,
  pagination?: Partial<PaginationOptions>
): Promise<ApiResponse<Match[]>> {
  const params = new URLSearchParams();
  
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, JSON.stringify(value));
      }
    });
  }
  
  if (sort) {
    params.append('sort', `${sort.field}:${sort.direction}`);
  }
  
  if (pagination) {
    if (pagination.page) params.append('page', pagination.page.toString());
    if (pagination.pageSize) params.append('pageSize', pagination.pageSize.toString());
  }
  
  const response = await fetch(`${API_BASE_URL}/matches?${params}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function getMatch(matchId: string): Promise<ApiResponse<Match>> {
  const response = await fetch(`${API_BASE_URL}/matches/${matchId}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function getLiveMatches(): Promise<ApiResponse<Match[]>> {
  const response = await fetch(`${API_BASE_URL}/matches/live`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function getUpcomingMatches(hours: number = 24): Promise<ApiResponse<Match[]>> {
  const response = await fetch(`${API_BASE_URL}/matches/upcoming?hours=${hours}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

// ========== ODDS ==========

export async function getOdds(matchId: string): Promise<ApiResponse<OddsData>> {
  const response = await fetch(`${API_BASE_URL}/odds/${matchId}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function getOddsHistory(matchId: string): Promise<ApiResponse<OddsData[]>> {
  const response = await fetch(`${API_BASE_URL}/odds/${matchId}/history`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function compareBestOdds(matchIds: string[]): Promise<ApiResponse<any>> {
  const response = await fetch(`${API_BASE_URL}/odds/compare`, {
    method: 'POST',
    headers: getHeaders(),
    body: JSON.stringify({ matchIds })
  });
  
  return response.json();
}

// ========== PREDICTIONS ==========

export async function getPrediction(matchId: string): Promise<ApiResponse<PredictionData>> {
  const response = await fetch(`${API_BASE_URL}/predictions/${matchId}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function getPredictions(matchIds: string[]): Promise<ApiResponse<PredictionData[]>> {
  const params = new URLSearchParams();
  params.append('ids', matchIds.join(','));
  
  const response = await fetch(`${API_BASE_URL}/predictions?${params}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

export async function getRecommendedBets(
  filters?: FilterOptions
): Promise<ApiResponse<any[]>> {
  const params = new URLSearchParams();
  
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, JSON.stringify(value));
      }
    });
  }
  
  const response = await fetch(`${API_BASE_URL}/predictions/recommended?${params}`, {
    headers: getHeaders()
  });
  
  return response.json();
}

// ========== BETTING ==========

export async function placeBet(
  betSlip: BetSlip,
  token: string
): Promise<ApiResponse<Bet>> {
  const response = await fetch(`${API_BASE_URL}/bets`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify(betSlip)
  });
  
  return response.json();
}

export async function getBets(
  token: string,
  filters?: FilterOptions,
  pagination?: Partial<PaginationOptions>
): Promise<ApiResponse<Bet[]>> {
  const params = new URLSearchParams();
  
  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, JSON.stringify(value));
      }
    });
  }
  
  if (pagination) {
    if (pagination.page) params.append('page', pagination.page.toString());
    if (pagination.pageSize) params.append('pageSize', pagination.pageSize.toString());
  }
  
  const response = await fetch(`${API_BASE_URL}/bets?${params}`, {
    headers: getHeaders(token)
  });
  
  return response.json();
}

export async function getBet(betId: string, token: string): Promise<ApiResponse<Bet>> {
  const response = await fetch(`${API_BASE_URL}/bets/${betId}`, {
    headers: getHeaders(token)
  });
  
  return response.json();
}

export async function cashOutBet(
  betId: string,
  amount: number,
  token: string
): Promise<ApiResponse<Bet>> {
  const response = await fetch(`${API_BASE_URL}/bets/${betId}/cashout`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify({ amount })
  });
  
  return response.json();
}

// ========== USER ==========

export async function login(
  email: string,
  password: string
): Promise<ApiResponse<{ user: User; token: string }>> {
  const response = await fetch(`${API_BASE_URL}/auth/login`, {
    method: 'POST',
    headers: getHeaders(),
    body: JSON.stringify({ email, password })
  });
  
  return response.json();
}

export async function register(
  userData: Partial<User> & { password: string }
): Promise<ApiResponse<{ user: User; token: string }>> {
  const response = await fetch(`${API_BASE_URL}/auth/register`, {
    method: 'POST',
    headers: getHeaders(),
    body: JSON.stringify(userData)
  });
  
  return response.json();
}

export async function getProfile(token: string): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/user/profile`, {
    headers: getHeaders(token)
  });
  
  return response.json();
}

export async function updateProfile(
  updates: Partial<User>,
  token: string
): Promise<ApiResponse<User>> {
  const response = await fetch(`${API_BASE_URL}/user/profile`, {
    method: 'PATCH',
    headers: getHeaders(token),
    body: JSON.stringify(updates)
  });
  
  return response.json();
}

export async function getUserStats(
  token: string,
  period?: string
): Promise<ApiResponse<any>> {
  const params = period ? `?period=${period}` : '';
  
  const response = await fetch(`${API_BASE_URL}/user/stats${params}`, {
    headers: getHeaders(token)
  });
  
  return response.json();
}

// ========== ANALYTICS ==========

export async function getAnalytics(
  token: string,
  dateRange?: { start: string; end: string }
): Promise<ApiResponse<AnalyticsData>> {
  const params = new URLSearchParams();
  
  if (dateRange) {
    params.append('startDate', dateRange.start);
    params.append('endDate', dateRange.end);
  }
  
  const response = await fetch(`${API_BASE_URL}/analytics?${params}`, {
    headers: getHeaders(token)
  });
  
  return response.json();
}

export async function exportData(
  options: ExportOptions,
  token: string
): Promise<Blob> {
  const response = await fetch(`${API_BASE_URL}/export`, {
    method: 'POST',
    headers: getHeaders(token),
    body: JSON.stringify(options)
  });
  
  return response.blob();
}

// ========== WEBSOCKET ==========

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private messageHandlers: Map<string, (message: WebSocketMessage) => void> = new Map();
  private token?: string;

  constructor(token?: string) {
    this.token = token;
  }

  connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN) return;

    const url = this.token ? `${WS_URL}?token=${this.token}` : WS_URL;
    this.ws = new WebSocket(url);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      if (this.reconnectTimeout) {
        clearTimeout(this.reconnectTimeout);
        this.reconnectTimeout = null;
      }
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  private reconnect(): void {
    if (this.reconnectTimeout) return;
    
    this.reconnectTimeout = setTimeout(() => {
      console.log('Attempting to reconnect WebSocket...');
      this.connect();
    }, 5000);
  }

  subscribe(channel: string, handler: (message: WebSocketMessage) => void): void {
    this.messageHandlers.set(channel, handler);
    
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'subscribe',
        channel
      }));
    }
  }

  unsubscribe(channel: string): void {
    this.messageHandlers.delete(channel);
    
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'unsubscribe',
        channel
      }));
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    const handler = this.messageHandlers.get(message.channel);
    if (handler) {
      handler(message);
    }
  }

  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// ========== CACHE ==========

export class CacheManager {
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private ttl: number = 5 * 60 * 1000; // 5 minutos por padrão

  set(key: string, data: any, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now() + (ttl || this.ttl)
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() > item.timestamp) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }

  remove(key: string): void {
    this.cache.delete(key);
  }
}

// Instância global do cache
export const cache = new CacheManager();

// ========== HELPERS ==========

export function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (typeof value === 'object') {
        searchParams.append(key, JSON.stringify(value));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });
  
  return searchParams.toString();
}

export async function fetchWithRetry<T>(
  url: string,
  options?: RequestInit,
  retries: number = 3
): Promise<T> {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      if (i === retries - 1) throw error;
      
      // Aguardar antes de tentar novamente
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
  
  throw new Error('Max retries reached');
}